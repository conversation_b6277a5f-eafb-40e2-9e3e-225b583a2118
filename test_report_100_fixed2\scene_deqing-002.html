
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>deqing-002 - 性能分析报告</title>
                <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
                <style>
                    body {
                        background: #1e1e1e;
                        color: #ffffff;
                        font-family: Microsoft YaHei, 'Segoe UI', Arial, sans-serif;
                        margin: 0;
                        padding: 20px;
                        min-height: 100vh;
                    }
                    .section {
                        margin: 30px 0;
                        padding: 25px;
                        background: #2d2d2d;
                        border-radius: 12px;
                        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                        border: 1px solid #404040;
                    }
                    .gauge-container {
                        display: flex;
                        justify-content: space-around;
                        align-items: center;
                        flex-wrap: wrap;
                    }
                    h1 {
                        text-align: center;
                        color: #ffffff;
                        font-weight: 300;
                        font-size: 2.5rem;
                        margin-bottom: 10px;
                        text-shadow: 0 2px 4px rgba(0,0,0,0.5);
                    }
                    h2 {
                        text-align: center;
                        color: #cccccc;
                        font-weight: 400;
                        font-size: 1.8rem;
                        margin-bottom: 20px;
                    }
                </style>
            </head>
            <body>
                <h1>deqing-002 - 性能分析报告</h1>

                <div class="section">
                    <h2>总体性能指标</h2>
                    
        <div id="gauge-container-deqing-002" style="width: 100%; height: 400px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: #2c3e50; margin-bottom: 15px;">
                    deqing-002 - 总体性能仪表盘
                </h3>
            </div>
            <div id="gauge-chart-deqing-002" style="width: 100%; height: 350px;"></div>
        </div>

        <script>
            // 初始化仪表盘
            var gaugeChart_deqing_002 = echarts.init(document.getElementById('gauge-chart-deqing-002'));

            var gaugeOption_deqing_002 = {
                backgroundColor: 'rgba(255, 255, 255, 0.5)',
                series: [
                    
            {
                name: '检出率',
                type: 'gauge',
                center: ['25%', '50%'],
                radius: '70%',
                min: 0,
                max: 100,
                splitNumber: 10,
                axisLine: {
                    lineStyle: {
                        color: [[0.3, '#e67e22'], [0.7, '#3498db'], [1, '#3498db']],
                        width: 8
                    }
                },
                pointer: {
                    itemStyle: { color: 'auto' }
                },
                axisTick: {
                    distance: -30,
                    length: 8,
                    lineStyle: { color: '#fff', width: 2 }
                },
                splitLine: {
                    distance: -30,
                    length: 30,
                    lineStyle: { color: '#fff', width: 4 }
                },
                axisLabel: {
                    color: 'auto',
                    distance: 40,
                    fontSize: 12
                },
                detail: {
                    valueAnimation: true,
                    formatter: '{value}%',
                    color: '#3498db',
                    fontSize: 16,
                    offsetCenter: [0, '70%']
                },
                title: {
                    offsetCenter: [0, '90%'],
                    fontSize: 14,
                    color: '#5a6c7d'
                },
                data: [{ value: 64.2, name: '检出率' }]
            }
            ,
                    
            {
                name: '准确率',
                type: 'gauge',
                center: ['75%', '50%'],
                radius: '70%',
                min: 0,
                max: 100,
                splitNumber: 10,
                axisLine: {
                    lineStyle: {
                        color: [[0.3, '#e67e22'], [0.7, '#f39c12'], [1, '#2ecc71']],
                        width: 8
                    }
                },
                pointer: {
                    itemStyle: { color: 'auto' }
                },
                axisTick: {
                    distance: -30,
                    length: 8,
                    lineStyle: { color: '#fff', width: 2 }
                },
                splitLine: {
                    distance: -30,
                    length: 30,
                    lineStyle: { color: '#fff', width: 4 }
                },
                axisLabel: {
                    color: 'auto',
                    distance: 40,
                    fontSize: 12
                },
                detail: {
                    valueAnimation: true,
                    formatter: '{value}%',
                    color: '#2ecc71',
                    fontSize: 16,
                    offsetCenter: [0, '70%']
                },
                title: {
                    offsetCenter: [0, '90%'],
                    fontSize: 14,
                    color: '#5a6c7d'
                },
                data: [{ value: 84.1, name: '准确率' }]
            }
            
                ]
            };

            gaugeChart_deqing_002.setOption(gaugeOption_deqing_002);
            window.addEventListener('resize', () => gaugeChart_deqing_002.resize());
        </script>
        
                </div>

                <div class="section">
                    <h2>距离段性能对比</h2>
                    
        <div id="bar-chart-deqing_002" style="width: 1000px; height: 500px; margin: 20px auto;"></div>

        <script>
            // 初始化柱状图
            var barChart_deqing_002 = echarts.init(document.getElementById('bar-chart-deqing_002'));

            var barOption_deqing_002 = {
  "backgroundColor": "#1e1e1e",
  "title": {
    "text": "deqing-002 - 距离段性能对比",
    "subtext": "不同目标类别在各距离段的准确率和检出率对比",
    "textStyle": {
      "color": "#ffffff",
      "fontSize": 16
    },
    "subtextStyle": {
      "color": "#cccccc",
      "fontSize": 12
    }
  },
  "tooltip": {
    "trigger": "axis",
    "backgroundColor": "rgba(45, 45, 45, 0.95)",
    "borderColor": "#404040",
    "textStyle": {
      "color": "#ffffff"
    }
  },
  "legend": {
    "top": "8%",
    "textStyle": {
      "color": "#ffffff"
    }
  },
  "xAxis": {
    "type": "category",
    "data": [
      "0-50米",
      "50-100米",
      "总体"
    ],
    "name": "距离段",
    "axisLabel": {
      "color": "#ffffff"
    },
    "axisLine": {
      "lineStyle": {
        "color": "#404040"
      }
    }
  },
  "yAxis": {
    "type": "value",
    "name": "性能指标 (%)",
    "min": 0,
    "max": 100,
    "axisLabel": {
      "color": "#ffffff"
    },
    "axisLine": {
      "lineStyle": {
        "color": "#404040"
      }
    },
    "splitLine": {
      "show": true,
      "lineStyle": {
        "color": "#404040",
        "type": "dashed"
      }
    }
  },
  "series": [
    {
      "name": "机动车-准确率",
      "type": "bar",
      "data": [
        86.6,
        75.7,
        81.3
      ],
      "itemStyle": {
        "color": "#2ca02c"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "机动车-检出率",
      "type": "bar",
      "data": [
        69.6,
        57.2,
        63.4
      ],
      "itemStyle": {
        "color": "#98df8a"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "行人-准确率",
      "type": "bar",
      "data": [
        95.9,
        89.6,
        93.0
      ],
      "itemStyle": {
        "color": "#1f77b4"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "行人-检出率",
      "type": "bar",
      "data": [
        78.4,
        65.2,
        72.1
      ],
      "itemStyle": {
        "color": "#aec7e8"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "非机动车-准确率",
      "type": "bar",
      "data": [
        82.1,
        70.9,
        77.1
      ],
      "itemStyle": {
        "color": "#ff7f0e"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "非机动车-检出率",
      "type": "bar",
      "data": [
        63.1,
        49.6,
        56.8
      ],
      "itemStyle": {
        "color": "#ffbb78"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    }
  ]
};

            barChart_deqing_002.setOption(barOption_deqing_002);
            window.addEventListener('resize', () => barChart_deqing_002.resize());
        </script>
        
                </div>

                <div class="section">
                    <h2>雷达图分析</h2>
                    
        <div id="radar-container-deqing-002" style="width: 100%; height: 600px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: #ffffff; margin-bottom: 15px;">
                    deqing-002 - 雷达图分析
                </h3>
                <div id="radar-buttons-deqing-002" style="margin-bottom: 20px;">
        
                    <button onclick="switchRadar_deqing_002('0-50米', this)"
                            class="radar-btn radar-active"
                            style="margin: 0 10px; padding: 8px 16px;
                                   background: #2d2d2d;
                                   color: #ffffff;
                                   border: 1px solid #404040;
                                   border-radius: 4px; cursor: pointer;">
                        0-50米
                    </button>
            
                    <button onclick="switchRadar_deqing_002('50-100米', this)"
                            class="radar-btn "
                            style="margin: 0 10px; padding: 8px 16px;
                                   background: #2d2d2d;
                                   color: #ffffff;
                                   border: 1px solid #404040;
                                   border-radius: 4px; cursor: pointer;">
                        50-100米
                    </button>
            
                    <button onclick="switchRadar_deqing_002('总体', this)"
                            class="radar-btn "
                            style="margin: 0 10px; padding: 8px 16px;
                                   background: #2d2d2d;
                                   color: #ffffff;
                                   border: 1px solid #404040;
                                   border-radius: 4px; cursor: pointer;">
                        总体
                    </button>
            
                </div>
            </div>
            <div id="radar-chart-deqing-002" style="width: 100%; height: 500px;"></div>
        </div>

        <script>
            // 雷达图数据
            var radarData_deqing_002 = {"0-50米": [86.58892128279884, 69.55503512880561, 95.8656330749354, 78.43551797040169, 82.12290502793296, 63.0901287553648], "50-100米": [75.6923076923077, 57.20930232558139, 89.62264150943396, 65.21739130434783, 70.93425605536332, 49.63680387409201], "总体": [81.2874251497006, 63.360560093348894, 93.04964539007092, 72.08791208791209, 77.12519319938175, 56.76905574516496]};

            // 雷达图配置
            var radarOption_deqing_002 = {
                backgroundColor: '#1e1e1e',
                radar: {
                    indicator: [
                        {name: '人-准确率', max: 100},
                        {name: '人-检出率', max: 100},
                        {name: '车-准确率', max: 100},
                        {name: '车-检出率', max: 100},
                        {name: '非-准确率', max: 100},
                        {name: '非-检出率', max: 100}
                    ],
                    center: ['50%', '50%'],
                    radius: '70%',
                    axisName: {
                        color: '#ffffff',
                        fontSize: 12
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#404040'
                        }
                    },
                    splitArea: {
                        show: false
                    }
                },
                series: [{
                    type: 'radar',
                    data: [{
                        value: radarData_deqing_002['0-50米'],
                        name: '0-50米',
                        areaStyle: {
                            color: 'rgba(0, 212, 255, 0.3)'
                        },
                        lineStyle: {
                            color: '#00d4ff'
                        }
                    }]
                }]
            };

            // 初始化雷达图
            var radarChart_deqing_002 = echarts.init(document.getElementById('radar-chart-deqing-002'));
            radarChart_deqing_002.setOption(radarOption_deqing_002);

            // 切换函数
            function switchRadar_deqing_002(distBin, clickedButton) {
                // 更新按钮状态
                var buttons = document.querySelectorAll('#radar-buttons-deqing-002 .radar-btn');
                buttons.forEach(btn => btn.classList.remove('radar-active'));
                clickedButton.classList.add('radar-active');

                // 更新雷达图数据
                radarOption_deqing_002.series[0].data[0].value = radarData_deqing_002[distBin];
                radarOption_deqing_002.series[0].data[0].name = distBin;
                radarChart_deqing_002.setOption(radarOption_deqing_002);
            }
        </script>

        <style>
            .radar-btn.radar-active {
                background: #00d4ff !important;
                color: #000000 !important;
            }
            .radar-btn:hover {
                background: #00d4ff !important;
                color: #000000 !important;
            }
        </style>
        
                </div>

                <div class="section">
                    <h2>时间趋势分析</h2>
                    
        <div id="trend-container-deqing-002" style="width: 100%; height: 700px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: #ffffff; margin-bottom: 15px;">
                    deqing-002 - 时间趋势分析
                </h3>
                <div id="trend-buttons-deqing-002" style="margin-bottom: 20px;">
        
                    <button onclick="switchTrend_deqing_002('0-50米', this)"
                            class="trend-btn trend-active"
                            style="margin: 0 10px; padding: 8px 16px;
                                   background: #2d2d2d;
                                   color: #ffffff;
                                   border: 1px solid #404040;
                                   border-radius: 4px; cursor: pointer;">
                        0-50米
                    </button>
            
                    <button onclick="switchTrend_deqing_002('50-100米', this)"
                            class="trend-btn "
                            style="margin: 0 10px; padding: 8px 16px;
                                   background: #2d2d2d;
                                   color: #ffffff;
                                   border: 1px solid #404040;
                                   border-radius: 4px; cursor: pointer;">
                        50-100米
                    </button>
            
                    <button onclick="switchTrend_deqing_002('总体', this)"
                            class="trend-btn "
                            style="margin: 0 10px; padding: 8px 16px;
                                   background: #2d2d2d;
                                   color: #ffffff;
                                   border: 1px solid #404040;
                                   border-radius: 4px; cursor: pointer;">
                        总体
                    </button>
            
                </div>
            </div>
            <div id="trend-chart-deqing-002" style="width: 100%; height: 600px;"></div>
        </div>

        <script>
            // 时间戳数据
            var timestamps_deqing_002 = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69];

            // 趋势图数据
            var trendData_deqing_002 = {"0-50米": {"机动车-准确率": [100.0, 75.0, 100.0, 72.7, 85.7, 75.0, 85.7, 100.0, 100.0, 100.0, 100.0, 100.0, 75.0, 100.0, 100.0, 71.4, 100.0, 100.0, 100.0, 100.0, 85.7, 66.7, 100.0, 71.4, 75.0, 100.0, 100.0, 80.0, 100.0, 77.8, 80.0, 80.0, 80.0, 100.0, 71.4, 100.0, 100.0, 100.0, 70.0, 80.0, 100.0, 83.3, 75.0, 100.0, 100.0, 83.3, 100.0, 100.0, 80.0, 100.0, 100.0, 100.0, 100.0, 100.0, 75.0, 100.0, 100.0, 100.0, 87.5, 75.0, 87.5, 100.0, 71.4, 100.0, 85.7, 100.0, 70.0, 100.0], "机动车-检出率": [75.0, 60.0, 66.7, 80.0, 75.0, 60.0, 60.0, 87.5, 75.0, 60.0, 75.0, 60.0, 50.0, 50.0, 66.7, 71.4, 60.0, 77.8, 66.7, 75.0, 60.0, 50.0, 50.0, 62.5, 60.0, 60.0, 66.7, 66.7, 33.3, 70.0, 88.9, 66.7, 57.1, 85.7, 71.4, 66.7, 60.0, 50.0, 77.8, 80.0, 62.5, 83.3, 60.0, 66.7, 57.1, 62.5, 75.0, 83.3, 80.0, 66.7, 66.7, 77.8, 60.0, 66.7, 75.0, 66.7, 66.7, 75.0, 70.0, 60.0, 77.8, 75.0, 83.3, 75.0, 85.7, 70.0, 77.8, 75.0], "行人-准确率": [100.0, 85.7, 100.0, 100.0, 100.0, 88.9, 100.0, 100.0, 87.5, 100.0, 100.0, 100.0, 85.7, 100.0, 100.0, 100.0, 85.7, 100.0, 100.0, 100.0, 87.5, 88.9, 100.0, 88.9, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 90.0, 87.5, 100.0, 100.0, 83.3, 100.0, 100.0, 100.0, 100.0, 88.9, 85.7, 100.0, 100.0, 100.0, 100.0, 100.0, 87.5, 100.0, 100.0, 88.9, 100.0, 100.0, 100.0, 100.0, 100.0, 88.9, 100.0, 100.0, 100.0], "行人-检出率": [75.0, 75.0, 80.0, 66.7, 71.4, 80.0, 80.0, 80.0, 87.5, 80.0, 75.0, 85.7, 85.7, 75.0, 77.8, 85.7, 75.0, 71.4, 60.0, 66.7, 70.0, 88.9, 75.0, 88.9, 60.0, 80.0, 66.7, 77.8, 60.0, 87.5, 85.7, 90.0, 87.5, 71.4, 66.7, 80.0, 71.4, 80.0, 75.0, 90.0, 87.5, 75.0, 71.4, 71.4, 80.0, 83.3, 66.7, 83.3, 80.0, 75.0, 60.0, 88.9, 80.0, 77.8, 66.7, 70.0, 87.5, 80.0, 88.9, 80.0, 75.0, 66.7, 66.7, 88.9, 80.0, 66.7, 80.0, 75.0], "非机动车-准确率": [100.0, 77.8, 80.0, 66.7, 100.0, 100.0, 66.7, 83.3, 100.0, 71.4, 100.0, 66.7, 100.0, 75.0, 100.0, 80.0, 80.0, 100.0, 83.3, 66.7, 66.7, 100.0, 63.6, 75.0, 87.5, 80.0, 83.3, 100.0, 100.0, 100.0, 71.4, 80.0, 83.3, 100.0, 100.0, 66.7, 100.0, 100.0, 71.4, 80.0, 80.0, 100.0, 100.0, 75.0, 83.3, 85.7, 83.3, 71.4, 77.8, 100.0, 66.7, 100.0, 75.0, 100.0, 66.7, 100.0, 100.0, 100.0, 71.4, 80.0, 66.7, 100.0, 100.0, 100.0, 83.3, 80.0, 75.0, 85.7], "非机动车-检出率": [60.0, 70.0, 50.0, 50.0, 60.0, 50.0, 50.0, 71.4, 83.3, 71.4, 50.0, 66.7, 57.1, 60.0, 57.1, 66.7, 66.7, 50.0, 55.6, 60.0, 75.0, 62.5, 70.0, 75.0, 70.0, 57.1, 55.6, 60.0, 66.7, 66.7, 62.5, 57.1, 55.6, 50.0, 80.0, 66.7, 60.0, 66.7, 71.4, 80.0, 57.1, 55.6, 66.7, 50.0, 71.4, 60.0, 62.5, 62.5, 77.8, 66.7, 80.0, 60.0, 60.0, 70.0, 50.0, 66.7, 50.0, 50.0, 55.6, 66.7, 66.7, 66.7, 62.5, 66.7, 62.5, 66.7, 50.0, 60.0]}, "50-100米": {"机动车-准确率": [100.0, 66.7, 62.5, 80.0, 60.0, 66.7, 100.0, 100.0, 66.7, 100.0, 71.4, 80.0, 100.0, 66.7, 85.7, 60.0, 66.7, 57.1, 75.0, 85.7, 100.0, 85.7, 53.8, 100.0, 63.6, 75.0, 100.0, 83.3, 57.1, 100.0, 66.7, 66.7, 100.0, 66.7, 75.0, 100.0, 100.0, 77.8, 85.7, 100.0, 80.0, 85.7, 100.0, 60.0, 66.7, 100.0, 87.5, 83.3, 100.0, 62.5, 100.0, 75.0, 66.7, 100.0, 100.0, 57.1, 80.0, 66.7, 66.7, 83.3, 57.1, 75.0, 75.0, 100.0, 60.0, 100.0, 100.0, 75.0], "机动车-检出率": [66.7, 60.0, 55.6, 50.0, 75.0, 40.0, 66.7, 66.7, 66.7, 33.3, 71.4, 44.4, 60.0, 40.0, 66.7, 66.7, 50.0, 44.4, 50.0, 75.0, 66.7, 66.7, 70.0, 50.0, 77.8, 60.0, 40.0, 62.5, 66.7, 60.0, 66.7, 50.0, 40.0, 50.0, 60.0, 70.0, 33.3, 70.0, 60.0, 50.0, 50.0, 75.0, 50.0, 60.0, 50.0, 33.3, 77.8, 50.0, 40.0, 71.4, 33.3, 60.0, 50.0, 60.0, 60.0, 44.4, 57.1, 66.7, 66.7, 50.0, 44.4, 50.0, 50.0, 40.0, 42.9, 50.0, 50.0, 60.0], "行人-准确率": [75.0, 100.0, 83.3, 83.3, 85.7, 100.0, 100.0, 87.5, 100.0, 100.0, 100.0, 80.0, 100.0, 100.0, 100.0, 75.0, 75.0, 100.0, 80.0, 100.0, 100.0, 85.7, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 75.0, 100.0, 87.5, 100.0, 83.3, 100.0, 100.0, 75.0, 100.0, 100.0, 83.3, 100.0, 85.7, 83.3, 85.7, 100.0, 100.0, 100.0, 80.0, 75.0, 75.0, 75.0, 100.0, 100.0, 70.0, 100.0, 66.7, 100.0, 100.0, 100.0, 100.0, 100.0, 83.3, 66.7, 100.0], "行人-检出率": [50.0, 57.1, 83.3, 83.3, 60.0, 75.0, 57.1, 77.8, 50.0, 66.7, 66.7, 80.0, 60.0, 50.0, 66.7, 66.7, 75.0, 60.0, 80.0, 66.7, 50.0, 66.7, 75.0, 55.6, 66.7, 50.0, 75.0, 50.0, 66.7, 80.0, 60.0, 75.0, 80.0, 66.7, 33.3, 70.0, 60.0, 62.5, 66.7, 66.7, 50.0, 57.1, 80.0, 71.4, 62.5, 75.0, 71.4, 60.0, 60.0, 60.0, 60.0, 80.0, 66.7, 60.0, 75.0, 66.7, 50.0, 70.0, 66.7, 66.7, 60.0, 50.0, 33.3, 57.1, 50.0, 62.5, 66.7, 66.7], "非机动车-准确率": [100.0, 50.0, 100.0, 66.7, 100.0, 100.0, 80.0, 100.0, 83.3, 100.0, 66.7, 100.0, 66.7, 75.0, 100.0, 60.0, 83.3, 80.0, 55.6, 80.0, 66.7, 57.1, 71.4, 60.0, 50.0, 50.0, 60.0, 50.0, 80.0, 100.0, 100.0, 80.0, 83.3, 80.0, 75.0, 80.0, 66.7, 50.0, 100.0, 100.0, 75.0, 75.0, 80.0, 85.7, 100.0, 100.0, 66.7, 100.0, 45.5, 100.0, 50.0, 100.0, 50.0, 100.0, 70.0, 66.7, 80.0, 80.0, 100.0, 100.0, 71.4, 54.5, 50.0, 100.0, 50.0, 100.0, 100.0, 100.0], "非机动车-检出率": [50.0, 55.6, 50.0, 50.0, 33.3, 37.5, 40.0, 33.3, 62.5, 25.0, 66.7, 33.3, 50.0, 33.3, 25.0, 42.9, 50.0, 50.0, 55.6, 44.4, 66.7, 57.1, 55.6, 37.5, 33.3, 50.0, 50.0, 25.0, 50.0, 33.3, 25.0, 50.0, 71.4, 44.4, 60.0, 40.0, 66.7, 50.0, 50.0, 50.0, 66.7, 60.0, 44.4, 66.7, 50.0, 50.0, 33.3, 66.7, 55.6, 25.0, 50.0, 25.0, 62.5, 50.0, 70.0, 40.0, 57.1, 66.7, 60.0, 66.7, 55.6, 75.0, 33.3, 33.3, 66.7, 33.3, 33.3, 33.3]}, "总体": {"机动车-准确率": [100.0, 70.8, 81.2, 76.4, 72.9, 70.8, 92.9, 100.0, 83.3, 100.0, 85.7, 90.0, 87.5, 83.3, 92.9, 65.7, 83.3, 78.6, 87.5, 92.9, 92.9, 76.2, 76.9, 85.7, 69.3, 87.5, 100.0, 81.7, 78.6, 88.9, 73.3, 73.3, 90.0, 83.3, 73.2, 100.0, 100.0, 88.9, 77.9, 90.0, 90.0, 84.5, 87.5, 80.0, 83.3, 91.7, 93.8, 91.7, 90.0, 81.2, 100.0, 87.5, 83.3, 100.0, 87.5, 78.6, 90.0, 83.3, 77.1, 79.2, 72.3, 87.5, 73.2, 100.0, 72.9, 100.0, 85.0, 87.5], "机动车-检出率": [70.8, 60.0, 61.1, 65.0, 75.0, 50.0, 63.3, 77.1, 70.8, 46.7, 73.2, 52.2, 55.0, 45.0, 66.7, 69.0, 55.0, 61.1, 58.3, 75.0, 63.3, 58.3, 60.0, 56.2, 68.9, 60.0, 53.3, 64.6, 50.0, 65.0, 77.8, 58.3, 48.6, 67.9, 65.7, 68.3, 46.7, 60.0, 68.9, 65.0, 56.2, 79.2, 55.0, 63.3, 53.6, 47.9, 76.4, 66.7, 60.0, 69.0, 50.0, 68.9, 55.0, 63.3, 67.5, 55.6, 61.9, 70.8, 68.3, 55.0, 61.1, 62.5, 66.7, 57.5, 64.3, 60.0, 63.9, 67.5], "行人-准确率": [87.5, 92.9, 91.7, 91.7, 92.9, 94.4, 100.0, 93.8, 93.8, 100.0, 100.0, 90.0, 92.9, 100.0, 100.0, 87.5, 80.4, 100.0, 90.0, 100.0, 93.8, 87.3, 100.0, 94.4, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 87.5, 100.0, 93.8, 100.0, 91.7, 100.0, 95.0, 81.2, 100.0, 100.0, 83.3, 100.0, 92.9, 91.7, 92.9, 94.4, 92.9, 100.0, 90.0, 87.5, 87.5, 87.5, 93.8, 100.0, 85.0, 94.4, 83.3, 100.0, 100.0, 100.0, 100.0, 94.4, 91.7, 83.3, 100.0], "行人-检出率": [62.5, 66.1, 81.7, 75.0, 65.7, 77.5, 68.6, 78.9, 68.8, 73.3, 70.8, 82.9, 72.9, 62.5, 72.2, 76.2, 75.0, 65.7, 70.0, 66.7, 60.0, 77.8, 75.0, 72.2, 63.3, 65.0, 70.8, 63.9, 63.3, 83.8, 72.9, 82.5, 83.8, 69.0, 50.0, 75.0, 65.7, 71.2, 70.8, 78.3, 68.8, 66.1, 75.7, 71.4, 71.2, 79.2, 69.0, 71.7, 70.0, 67.5, 60.0, 84.4, 73.3, 68.9, 70.8, 68.3, 68.8, 75.0, 77.8, 73.3, 67.5, 58.3, 50.0, 73.0, 65.0, 64.6, 73.3, 70.8], "非机动车-准确率": [100.0, 63.9, 90.0, 66.7, 100.0, 100.0, 73.3, 91.7, 91.7, 85.7, 83.3, 83.3, 83.3, 75.0, 100.0, 70.0, 81.7, 90.0, 69.4, 73.3, 66.7, 78.6, 67.5, 67.5, 68.8, 65.0, 71.7, 75.0, 90.0, 100.0, 85.7, 80.0, 83.3, 90.0, 87.5, 73.3, 83.3, 75.0, 85.7, 90.0, 77.5, 87.5, 90.0, 80.4, 91.7, 92.9, 75.0, 85.7, 61.6, 100.0, 58.3, 100.0, 62.5, 100.0, 68.3, 83.3, 90.0, 90.0, 85.7, 90.0, 69.0, 77.3, 75.0, 100.0, 66.7, 90.0, 87.5, 92.9], "非机动车-检出率": [55.0, 62.8, 50.0, 50.0, 46.7, 43.8, 45.0, 52.4, 72.9, 48.2, 58.3, 50.0, 53.6, 46.7, 41.1, 54.8, 58.3, 50.0, 55.6, 52.2, 70.8, 59.8, 62.8, 56.2, 51.7, 53.6, 52.8, 42.5, 58.3, 50.0, 43.8, 53.6, 63.5, 47.2, 70.0, 53.3, 63.3, 58.3, 60.7, 65.0, 61.9, 57.8, 55.6, 58.3, 60.7, 55.0, 47.9, 64.6, 66.7, 45.8, 65.0, 42.5, 61.3, 60.0, 60.0, 53.3, 53.6, 58.3, 57.8, 66.7, 61.1, 70.8, 47.9, 50.0, 64.6, 50.0, 41.7, 46.7]}};

            // 颜色配置
            var categoryColors_deqing_002 = {
                '行人': ['#1f77b4', '#aec7e8'],
                '机动车': ['#2ca02c', '#98df8a'],
                '非机动车': ['#ff7f0e', '#ffbb78']
            };

            // 趋势图配置
            function getTrendOption_deqing_002(distBin) {
                var seriesData = trendData_deqing_002[distBin];
                var series = [];

                Object.keys(seriesData).forEach(function(seriesName) {
                    var category = seriesName.split('-')[0];
                    var metric = seriesName.split('-')[1];
                    var colorIndex = metric === '准确率' ? 0 : 1;
                    var lineType = metric === '准确率' ? 'solid' : 'dashed';

                    series.push({
                        name: seriesName,
                        type: 'line',
                        data: seriesData[seriesName],
                        smooth: false,
                        symbol: 'none',
                        lineStyle: {
                            color: categoryColors_deqing_002[category][colorIndex],
                            width: 3,
                            type: lineType
                        },
                        emphasis: {
                            focus: 'series',
                            lineStyle: {
                                width: 4
                            }
                        }
                    });
                });

                return {
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    title: {
                        text: distBin + ' - 性能趋势',
                        left: 'center',
                        textStyle: {
                            color: '#5a6c7d',
                            fontSize: 16
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        borderColor: '#bdc3c7',
                        textStyle: {
                            color: '#2c3e50'
                        }
                    },
                    legend: {
                        top: '8%',
                        textStyle: {
                            color: '#5a6c7d'
                        }
                    },
                    grid: {
                        left: '10%',
                        right: '10%',
                        top: '20%',
                        bottom: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: timestamps_deqing_002,
                        name: '帧号',
                        axisLabel: {
                            color: '#7f8c8d'
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#bdc3c7'
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '性能指标 (%)',
                        min: 0,
                        max: 100,
                        axisLabel: {
                            color: '#7f8c8d'
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#bdc3c7'
                            }
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#ecf0f1',
                                type: 'dashed'
                            }
                        }
                    },
                    series: series
                };
            }

            // 初始化趋势图
            var trendChart_deqing_002 = echarts.init(document.getElementById('trend-chart-deqing-002'));
            trendChart_deqing_002.setOption(getTrendOption_deqing_002('0-50米'));

            // 切换函数
            function switchTrend_deqing_002(distBin, clickedButton) {
                // 更新按钮状态
                var buttons = document.querySelectorAll('#trend-buttons-deqing-002 .trend-btn');
                buttons.forEach(btn => btn.classList.remove('trend-active'));
                clickedButton.classList.add('trend-active');

                // 更新趋势图
                trendChart_deqing_002.setOption(getTrendOption_deqing_002(distBin));
            }
        </script>

        <style>
            .trend-btn.trend-active {
                background: #00d4ff !important;
                color: #000000 !important;
            }
            .trend-btn:hover {
                background: #00d4ff !important;
                color: #000000 !important;
            }
        </style>
        
                </div>
            </body>
            </html>
            