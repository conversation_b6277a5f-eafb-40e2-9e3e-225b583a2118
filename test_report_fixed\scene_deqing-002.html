
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>deqing-002 - 性能分析报告</title>
                <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
                <style>
                    body {
                        background: #1e1e1e;
                        color: #ffffff;
                        font-family: Microsoft YaHei, 'Segoe UI', Arial, sans-serif;
                        margin: 0;
                        padding: 20px;
                        min-height: 100vh;
                    }
                    .section {
                        margin: 30px 0;
                        padding: 25px;
                        background: #2d2d2d;
                        border-radius: 12px;
                        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                        border: 1px solid #404040;
                    }
                    .gauge-container {
                        display: flex;
                        justify-content: space-around;
                        align-items: center;
                        flex-wrap: wrap;
                    }
                    h1 {
                        text-align: center;
                        color: #ffffff;
                        font-weight: 300;
                        font-size: 2.5rem;
                        margin-bottom: 10px;
                        text-shadow: 0 2px 4px rgba(0,0,0,0.5);
                    }
                    h2 {
                        text-align: center;
                        color: #cccccc;
                        font-weight: 400;
                        font-size: 1.8rem;
                        margin-bottom: 20px;
                    }
                </style>
            </head>
            <body>
                <h1>deqing-002 - 性能分析报告</h1>

                <div class="section">
                    <h2>总体性能指标</h2>
                    
        <div id="gauge-container-deqing-002" style="width: 100%; height: 400px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: #2c3e50; margin-bottom: 15px;">
                    deqing-002 - 总体性能仪表盘
                </h3>
            </div>
            <div id="gauge-chart-deqing-002" style="width: 100%; height: 350px;"></div>
        </div>

        <script>
            // 初始化仪表盘
            var gaugeChart_deqing_002 = echarts.init(document.getElementById('gauge-chart-deqing-002'));

            var gaugeOption_deqing_002 = {
                backgroundColor: 'rgba(255, 255, 255, 0.5)',
                series: [
                    
            {
                name: '检出率',
                type: 'gauge',
                center: ['25%', '50%'],
                radius: '70%',
                min: 0,
                max: 100,
                splitNumber: 10,
                axisLine: {
                    lineStyle: {
                        color: [[0.3, '#e67e22'], [0.7, '#3498db'], [1, '#3498db']],
                        width: 8
                    }
                },
                pointer: {
                    itemStyle: { color: 'auto' }
                },
                axisTick: {
                    distance: -30,
                    length: 8,
                    lineStyle: { color: '#fff', width: 2 }
                },
                splitLine: {
                    distance: -30,
                    length: 30,
                    lineStyle: { color: '#fff', width: 4 }
                },
                axisLabel: {
                    color: 'auto',
                    distance: 40,
                    fontSize: 12
                },
                detail: {
                    valueAnimation: true,
                    formatter: '{value}%',
                    color: '#3498db',
                    fontSize: 16,
                    offsetCenter: [0, '70%']
                },
                title: {
                    offsetCenter: [0, '90%'],
                    fontSize: 14,
                    color: '#5a6c7d'
                },
                data: [{ value: 69.2, name: '检出率' }]
            }
            ,
                    
            {
                name: '准确率',
                type: 'gauge',
                center: ['75%', '50%'],
                radius: '70%',
                min: 0,
                max: 100,
                splitNumber: 10,
                axisLine: {
                    lineStyle: {
                        color: [[0.3, '#e67e22'], [0.7, '#f39c12'], [1, '#2ecc71']],
                        width: 8
                    }
                },
                pointer: {
                    itemStyle: { color: 'auto' }
                },
                axisTick: {
                    distance: -30,
                    length: 8,
                    lineStyle: { color: '#fff', width: 2 }
                },
                splitLine: {
                    distance: -30,
                    length: 30,
                    lineStyle: { color: '#fff', width: 4 }
                },
                axisLabel: {
                    color: 'auto',
                    distance: 40,
                    fontSize: 12
                },
                detail: {
                    valueAnimation: true,
                    formatter: '{value}%',
                    color: '#2ecc71',
                    fontSize: 16,
                    offsetCenter: [0, '70%']
                },
                title: {
                    offsetCenter: [0, '90%'],
                    fontSize: 14,
                    color: '#5a6c7d'
                },
                data: [{ value: 84.4, name: '准确率' }]
            }
            
                ]
            };

            gaugeChart_deqing_002.setOption(gaugeOption_deqing_002);
            window.addEventListener('resize', () => gaugeChart_deqing_002.resize());
        </script>
        
                </div>

                <div class="section">
                    <h2>距离段性能对比</h2>
                    
        <div id="bar-chart-deqing_002" style="width: 1000px; height: 500px; margin: 20px auto;"></div>

        <script>
            // 初始化柱状图
            var barChart_deqing_002 = echarts.init(document.getElementById('bar-chart-deqing_002'));

            var barOption_deqing_002 = {
  "backgroundColor": "#1e1e1e",
  "title": {
    "text": "deqing-002 - 距离段性能对比",
    "subtext": "不同目标类别在各距离段的准确率和检出率对比",
    "textStyle": {
      "color": "#ffffff",
      "fontSize": 16
    },
    "subtextStyle": {
      "color": "#cccccc",
      "fontSize": 12
    }
  },
  "tooltip": {
    "trigger": "axis",
    "backgroundColor": "rgba(45, 45, 45, 0.95)",
    "borderColor": "#404040",
    "textStyle": {
      "color": "#ffffff"
    }
  },
  "legend": {
    "top": "8%",
    "textStyle": {
      "color": "#ffffff"
    }
  },
  "xAxis": {
    "type": "category",
    "data": [
      "0-50米",
      "50-100米",
      "总体"
    ],
    "name": "距离段",
    "axisLabel": {
      "color": "#ffffff"
    },
    "axisLine": {
      "lineStyle": {
        "color": "#404040"
      }
    }
  },
  "yAxis": {
    "type": "value",
    "name": "性能指标 (%)",
    "min": 0,
    "max": 100,
    "axisLabel": {
      "color": "#ffffff"
    },
    "axisLine": {
      "lineStyle": {
        "color": "#404040"
      }
    },
    "splitLine": {
      "show": true,
      "lineStyle": {
        "color": "#404040",
        "type": "dashed"
      }
    }
  },
  "series": [
    {
      "name": "机动车-准确率",
      "type": "bar",
      "data": [
        73.8,
        73.8,
        73.8
      ],
      "itemStyle": {
        "color": "#2ca02c"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "机动车-检出率",
      "type": "bar",
      "data": [
        54.1,
        54.1,
        54.1
      ],
      "itemStyle": {
        "color": "#98df8a"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "行人-准确率",
      "type": "bar",
      "data": [
        100.0,
        100.0,
        100.0
      ],
      "itemStyle": {
        "color": "#1f77b4"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "行人-检出率",
      "type": "bar",
      "data": [
        88.5,
        88.5,
        88.5
      ],
      "itemStyle": {
        "color": "#aec7e8"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "非机动车-准确率",
      "type": "bar",
      "data": [
        89.3,
        89.3,
        89.3
      ],
      "itemStyle": {
        "color": "#ff7f0e"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "非机动车-检出率",
      "type": "bar",
      "data": [
        84.7,
        84.7,
        84.7
      ],
      "itemStyle": {
        "color": "#ffbb78"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    }
  ]
};

            barChart_deqing_002.setOption(barOption_deqing_002);
            window.addEventListener('resize', () => barChart_deqing_002.resize());
        </script>
        
                </div>

                <div class="section">
                    <h2>雷达图分析</h2>
                    
        <div id="radar-container-deqing-002" style="width: 100%; height: 600px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: #ffffff; margin-bottom: 15px;">
                    deqing-002 - 雷达图分析
                </h3>
                <div id="radar-buttons-deqing-002" style="margin-bottom: 20px;">
        
                    <button onclick="switchRadar_deqing_002('0-50米', this)"
                            class="radar-btn radar-active"
                            style="margin: 0 10px; padding: 8px 16px;
                                   background: #2d2d2d;
                                   color: #ffffff;
                                   border: 1px solid #404040;
                                   border-radius: 4px; cursor: pointer;">
                        0-50米
                    </button>
            
                    <button onclick="switchRadar_deqing_002('50-100米', this)"
                            class="radar-btn "
                            style="margin: 0 10px; padding: 8px 16px;
                                   background: #2d2d2d;
                                   color: #ffffff;
                                   border: 1px solid #404040;
                                   border-radius: 4px; cursor: pointer;">
                        50-100米
                    </button>
            
                    <button onclick="switchRadar_deqing_002('总体', this)"
                            class="radar-btn "
                            style="margin: 0 10px; padding: 8px 16px;
                                   background: #2d2d2d;
                                   color: #ffffff;
                                   border: 1px solid #404040;
                                   border-radius: 4px; cursor: pointer;">
                        总体
                    </button>
            
                </div>
            </div>
            <div id="radar-chart-deqing-002" style="width: 100%; height: 500px;"></div>
        </div>

        <script>
            // 雷达图数据
            var radarData_deqing_002 = {"0-50米": [73.79679144385027, 54.11764705882353, 100.0, 88.46153846153845, 89.28571428571429, 84.7457627118644], "50-100米": [73.79679144385027, 54.11764705882353, 100.0, 88.46153846153845, 89.28571428571429, 84.7457627118644], "总体": [73.79679144385027, 54.11764705882353, 100.0, 88.46153846153845, 89.28571428571429, 84.7457627118644]};

            // 雷达图配置
            var radarOption_deqing_002 = {
                backgroundColor: '#1e1e1e',
                radar: {
                    indicator: [
                        {name: '人-准确率', max: 100},
                        {name: '人-检出率', max: 100},
                        {name: '车-准确率', max: 100},
                        {name: '车-检出率', max: 100},
                        {name: '非-准确率', max: 100},
                        {name: '非-检出率', max: 100}
                    ],
                    center: ['50%', '50%'],
                    radius: '70%',
                    axisName: {
                        color: '#ffffff',
                        fontSize: 12
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#404040'
                        }
                    },
                    splitArea: {
                        show: false
                    }
                },
                series: [{
                    type: 'radar',
                    data: [{
                        value: radarData_deqing_002['0-50米'],
                        name: '0-50米',
                        areaStyle: {
                            color: 'rgba(0, 212, 255, 0.3)'
                        },
                        lineStyle: {
                            color: '#00d4ff'
                        }
                    }]
                }]
            };

            // 初始化雷达图
            var radarChart_deqing_002 = echarts.init(document.getElementById('radar-chart-deqing-002'));
            radarChart_deqing_002.setOption(radarOption_deqing_002);

            // 切换函数
            function switchRadar_deqing_002(distBin, clickedButton) {
                // 更新按钮状态
                var buttons = document.querySelectorAll('#radar-buttons-deqing-002 .radar-btn');
                buttons.forEach(btn => btn.classList.remove('radar-active'));
                clickedButton.classList.add('radar-active');

                // 更新雷达图数据
                radarOption_deqing_002.series[0].data[0].value = radarData_deqing_002[distBin];
                radarOption_deqing_002.series[0].data[0].name = distBin;
                radarChart_deqing_002.setOption(radarOption_deqing_002);
            }
        </script>

        <style>
            .radar-btn.radar-active {
                background: #00d4ff !important;
                color: #000000 !important;
            }
            .radar-btn:hover {
                background: #00d4ff !important;
                color: #000000 !important;
            }
        </style>
        
                </div>

                <div class="section">
                    <h2>时间趋势分析</h2>
                    
        <div id="trend-container-deqing-002" style="width: 100%; height: 700px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: #ffffff; margin-bottom: 15px;">
                    deqing-002 - 时间趋势分析
                </h3>
                <div id="trend-buttons-deqing-002" style="margin-bottom: 20px;">
        
                    <button onclick="switchTrend_deqing_002('0-50米', this)"
                            class="trend-btn trend-active"
                            style="margin: 0 10px; padding: 8px 16px;
                                   background: #2d2d2d;
                                   color: #ffffff;
                                   border: 1px solid #404040;
                                   border-radius: 4px; cursor: pointer;">
                        0-50米
                    </button>
            
                    <button onclick="switchTrend_deqing_002('50-100米', this)"
                            class="trend-btn "
                            style="margin: 0 10px; padding: 8px 16px;
                                   background: #2d2d2d;
                                   color: #ffffff;
                                   border: 1px solid #404040;
                                   border-radius: 4px; cursor: pointer;">
                        50-100米
                    </button>
            
                    <button onclick="switchTrend_deqing_002('总体', this)"
                            class="trend-btn "
                            style="margin: 0 10px; padding: 8px 16px;
                                   background: #2d2d2d;
                                   color: #ffffff;
                                   border: 1px solid #404040;
                                   border-radius: 4px; cursor: pointer;">
                        总体
                    </button>
            
                </div>
            </div>
            <div id="trend-chart-deqing-002" style="width: 100%; height: 600px;"></div>
        </div>

        <script>
            // 时间戳数据
            var timestamps_deqing_002 = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61];

            // 趋势图数据
            var trendData_deqing_002 = {"0-50米": {"机动车-准确率": [100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 50.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 66.7, 80.0, 83.3, 83.3, 83.3, 75.0, 71.4, 71.4, 71.4, 71.4, 62.5, 62.5, 62.5, 62.5, 62.5, 66.7, 66.7, 50.0, 50.0, 50.0, 50.0, 50.0, 50.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 50.0, 100.0, 66.7, 66.7, 50.0, 50.0, 50.0, 50.0, 50.0, 50.0, 73.8], "机动车-检出率": [33.3, 33.3, 42.9, 50.0, 33.3, 16.7, 16.7, 20.0, 20.0, 16.7, 16.7, 33.3, 33.3, 33.3, 33.3, 57.1, 57.1, 62.5, 62.5, 62.5, 66.7, 62.5, 62.5, 62.5, 62.5, 62.5, 62.5, 62.5, 62.5, 62.5, 66.7, 66.7, 50.0, 50.0, 50.0, 50.0, 50.0, 50.0, 50.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 54.1], "行人-准确率": [100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 0, 100.0, 100.0, 100.0, 100.0, 0, 0.0, 0.0, 100.0, 100.0, 100.0, 0, 100.0, 0, 0, 0, 0, 0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 0.0, 0, 0, 0, 0, 0, 0, 100.0], "行人-检出率": [100.0, 100.0, 100.0, 100.0, 66.7, 66.7, 66.7, 100.0, 100.0, 100.0, 66.7, 75.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 0, 100.0, 100.0, 100.0, 100.0, 0, 0.0, 0.0, 50.0, 100.0, 100.0, 0, 100.0, 0, 0, 0, 0, 0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 0.0, 0, 0, 0, 0, 0, 0, 88.5], "非机动车-准确率": [0.0, 100.0, 0.0, 100.0, 100.0, 100.0, 0.0, 100.0, 100.0, 25.0, 100.0, 100.0, 100.0, 100.0, 100.0, 50.0, 50.0, 50.0, 50.0, 50.0, 0, 100.0, 100.0, 100.0, 0, 0, 100.0, 100.0, 100.0, 0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 0, 100.0, 66.7, 66.7, 100.0, 100.0, 100.0, 89.3], "非机动车-检出率": [0.0, 50.0, 0.0, 50.0, 50.0, 50.0, 0.0, 100.0, 83.3, 16.7, 85.7, 100.0, 100.0, 50.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 0, 100.0, 100.0, 100.0, 0, 0, 100.0, 100.0, 100.0, 0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 0, 100.0, 100.0, 100.0, 50.0, 100.0, 100.0, 84.7]}, "50-100米": {"机动车-准确率": [100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 50.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 66.7, 80.0, 83.3, 83.3, 83.3, 75.0, 71.4, 71.4, 71.4, 71.4, 62.5, 62.5, 62.5, 62.5, 62.5, 66.7, 66.7, 50.0, 50.0, 50.0, 50.0, 50.0, 50.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 50.0, 100.0, 66.7, 66.7, 50.0, 50.0, 50.0, 50.0, 50.0, 50.0, 73.8], "机动车-检出率": [33.3, 33.3, 42.9, 50.0, 33.3, 16.7, 16.7, 20.0, 20.0, 16.7, 16.7, 33.3, 33.3, 33.3, 33.3, 57.1, 57.1, 62.5, 62.5, 62.5, 66.7, 62.5, 62.5, 62.5, 62.5, 62.5, 62.5, 62.5, 62.5, 62.5, 66.7, 66.7, 50.0, 50.0, 50.0, 50.0, 50.0, 50.0, 50.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 54.1], "行人-准确率": [100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 0, 100.0, 100.0, 100.0, 100.0, 0, 0.0, 0.0, 100.0, 100.0, 100.0, 0, 100.0, 0, 0, 0, 0, 0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 0.0, 0, 0, 0, 0, 0, 0, 100.0], "行人-检出率": [100.0, 100.0, 100.0, 100.0, 66.7, 66.7, 66.7, 100.0, 100.0, 100.0, 66.7, 75.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 0, 100.0, 100.0, 100.0, 100.0, 0, 0.0, 0.0, 50.0, 100.0, 100.0, 0, 100.0, 0, 0, 0, 0, 0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 0.0, 0, 0, 0, 0, 0, 0, 88.5], "非机动车-准确率": [0.0, 100.0, 0.0, 100.0, 100.0, 100.0, 0.0, 100.0, 100.0, 25.0, 100.0, 100.0, 100.0, 100.0, 100.0, 50.0, 50.0, 50.0, 50.0, 50.0, 0, 100.0, 100.0, 100.0, 0, 0, 100.0, 100.0, 100.0, 0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 0, 100.0, 66.7, 66.7, 100.0, 100.0, 100.0, 89.3], "非机动车-检出率": [0.0, 50.0, 0.0, 50.0, 50.0, 50.0, 0.0, 100.0, 83.3, 16.7, 85.7, 100.0, 100.0, 50.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 0, 100.0, 100.0, 100.0, 0, 0, 100.0, 100.0, 100.0, 0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 0, 100.0, 100.0, 100.0, 50.0, 100.0, 100.0, 84.7]}, "总体": {"机动车-准确率": [100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 50.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 66.7, 80.0, 83.3, 83.3, 83.3, 75.0, 71.4, 71.4, 71.4, 71.4, 62.5, 62.5, 62.5, 62.5, 62.5, 66.7, 66.7, 50.0, 50.0, 50.0, 50.0, 50.0, 50.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 50.0, 100.0, 66.7, 66.7, 50.0, 50.0, 50.0, 50.0, 50.0, 50.0, 73.8], "机动车-检出率": [33.3, 33.3, 42.9, 50.0, 33.3, 16.7, 16.7, 20.0, 20.0, 16.7, 16.7, 33.3, 33.3, 33.3, 33.3, 57.1, 57.1, 62.5, 62.5, 62.5, 66.7, 62.5, 62.5, 62.5, 62.5, 62.5, 62.5, 62.5, 62.5, 62.5, 66.7, 66.7, 50.0, 50.0, 50.0, 50.0, 50.0, 50.0, 50.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 54.1], "行人-准确率": [100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 0, 100.0, 100.0, 100.0, 100.0, 0, 0.0, 0.0, 100.0, 100.0, 100.0, 0, 100.0, 0, 0, 0, 0, 0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 0.0, 0, 0, 0, 0, 0, 0, 100.0], "行人-检出率": [100.0, 100.0, 100.0, 100.0, 66.7, 66.7, 66.7, 100.0, 100.0, 100.0, 66.7, 75.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 0, 100.0, 100.0, 100.0, 100.0, 0, 0.0, 0.0, 50.0, 100.0, 100.0, 0, 100.0, 0, 0, 0, 0, 0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 0.0, 0, 0, 0, 0, 0, 0, 88.5], "非机动车-准确率": [0.0, 100.0, 0.0, 100.0, 100.0, 100.0, 0.0, 100.0, 100.0, 25.0, 100.0, 100.0, 100.0, 100.0, 100.0, 50.0, 50.0, 50.0, 50.0, 50.0, 0, 100.0, 100.0, 100.0, 0, 0, 100.0, 100.0, 100.0, 0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 0, 100.0, 66.7, 66.7, 100.0, 100.0, 100.0, 89.3], "非机动车-检出率": [0.0, 50.0, 0.0, 50.0, 50.0, 50.0, 0.0, 100.0, 83.3, 16.7, 85.7, 100.0, 100.0, 50.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 0, 100.0, 100.0, 100.0, 0, 0, 100.0, 100.0, 100.0, 0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 0, 100.0, 100.0, 100.0, 50.0, 100.0, 100.0, 84.7]}};

            // 颜色配置
            var categoryColors_deqing_002 = {
                '行人': ['#1f77b4', '#aec7e8'],
                '机动车': ['#2ca02c', '#98df8a'],
                '非机动车': ['#ff7f0e', '#ffbb78']
            };

            // 趋势图配置
            function getTrendOption_deqing_002(distBin) {
                var seriesData = trendData_deqing_002[distBin];
                var series = [];

                Object.keys(seriesData).forEach(function(seriesName) {
                    var category = seriesName.split('-')[0];
                    var metric = seriesName.split('-')[1];
                    var colorIndex = metric === '准确率' ? 0 : 1;
                    var lineType = metric === '准确率' ? 'solid' : 'dashed';

                    series.push({
                        name: seriesName,
                        type: 'line',
                        data: seriesData[seriesName],
                        smooth: false,
                        symbol: 'none',
                        lineStyle: {
                            color: categoryColors_deqing_002[category][colorIndex],
                            width: 3,
                            type: lineType
                        },
                        emphasis: {
                            focus: 'series',
                            lineStyle: {
                                width: 4
                            }
                        }
                    });
                });

                return {
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    title: {
                        text: distBin + ' - 性能趋势',
                        left: 'center',
                        textStyle: {
                            color: '#5a6c7d',
                            fontSize: 16
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        borderColor: '#bdc3c7',
                        textStyle: {
                            color: '#2c3e50'
                        }
                    },
                    legend: {
                        top: '8%',
                        textStyle: {
                            color: '#5a6c7d'
                        }
                    },
                    grid: {
                        left: '10%',
                        right: '10%',
                        top: '20%',
                        bottom: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: timestamps_deqing_002,
                        name: '帧号',
                        axisLabel: {
                            color: '#7f8c8d'
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#bdc3c7'
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '性能指标 (%)',
                        min: 0,
                        max: 100,
                        axisLabel: {
                            color: '#7f8c8d'
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#bdc3c7'
                            }
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#ecf0f1',
                                type: 'dashed'
                            }
                        }
                    },
                    series: series
                };
            }

            // 初始化趋势图
            var trendChart_deqing_002 = echarts.init(document.getElementById('trend-chart-deqing-002'));
            trendChart_deqing_002.setOption(getTrendOption_deqing_002('0-50米'));

            // 切换函数
            function switchTrend_deqing_002(distBin, clickedButton) {
                // 更新按钮状态
                var buttons = document.querySelectorAll('#trend-buttons-deqing-002 .trend-btn');
                buttons.forEach(btn => btn.classList.remove('trend-active'));
                clickedButton.classList.add('trend-active');

                // 更新趋势图
                trendChart_deqing_002.setOption(getTrendOption_deqing_002(distBin));
            }
        </script>

        <style>
            .trend-btn.trend-active {
                background: #00d4ff !important;
                color: #000000 !important;
            }
            .trend-btn:hover {
                background: #00d4ff !important;
                color: #000000 !important;
            }
        </style>
        
                </div>
            </body>
            </html>
            