# 项目需求说明书（Requirements Specification）✅

## 1. 项目背景 ✅
本项目旨在将多场景检测任务的性能结果（Excel 数据）转换为可视化的、易于分享的纯静态 HTML 报告。系统已成功实现从2个场景扩展到50个场景的大规模数据处理，采用现代化的柔和扁平化设计风格，支持21,120行数据的高效处理和可视化。

## 2. 目标读者
- 算法/研发团队：关注指标细节与问题定位。
- 产品/项目经理：关注总体趋势与跨场景表现。
- 领导层：关注关键 KPI（检出率、准确率、F1 等）。

## 3. 功能需求

### 3.0 核心指标定义
- **仅处理2个核心指标**：准确率(Precision) 和 检出率(Recall)
- **派生指标**：F1分数作为整体评价指标
- **目标类别**：人、车、非机动车
- **距离段**：总体(0-150米)、0-50米、50-100米、100-150米(如果数据存在)

### 3.1 数据处理 ✅
1. ✅ 支持批量读取多场景 Excel 文件（`.xlsx`）- 已测试50个场景。
2. ✅ 统一字段映射：`scene_id`、`timestamp`、`distance_bin`、`target_cls`、`tp`、`fp`、`fn`。
3. ✅ 自动计算派生指标：`recall`、`precision`、`f1`、`sample_cnt`。
4. ✅ 支持自定义距离分桶和类别映射配置。
5. ✅ 场景名称映射：支持使用原始文件名（如deqing-001）或中文映射。
6. ✅ 大规模数据处理：21,120行原始数据，6种维度聚合。

### 3.2 单场景分析页面 ✅

**总体指标展示** ✅：
- ✅ 3个指针式表盘：总体检出率、总体准确率、总体F1分数
- ✅ 刻度范围：0-100%
- ✅ 颜色阈值：绿色>80%，蓝色60%-80%，橙色<60%（柔和配色）

**分组柱状图** ✅：
- ✅ 3个距离段组：总体、0-50米、50-100米
- ✅ 每组6根柱子：人准确率、人检出率、车准确率、车检出率、非准确率、非检出率
- ✅ Y轴：0-100%

**雷达图分析** ✅：
- ✅ 单个雷达图 + 距离段切换功能
- ✅ 6个维度：人准确率、人检出率、车准确率、车检出率、非准确率、非检出率
- ✅ 切换按钮在下方：总体、0-50米、50-100米

**时间趋势分析** ✅：
- ✅ X轴：帧号(timestamp)，Y轴：指标值(0-100%)
- ✅ 距离段切换：每个距离段显示6条线(3类别×2指标)
- ✅ 支持距离段切换显示：总体、0-50米、50-100米
- ✅ 完整时间序列：79个连续时间戳的趋势分析
- ✅ 折线图样式：直线连接，无数据点标记

### 3.3 全场景分析页面 ✅

**总体概览 - 卡片风格** ✅：
- ✅ 4张卡片：所有场景平均总体准确率、检出率、F1分数、场景数量统计
- ✅ 柔和扁平化设计：毛玻璃效果、渐变背景、温和配色

**分类别概览 - 卡片风格** ✅：
- ✅ 6张卡片：所有场景平均人/车/非的准确率和检出率
- ✅ 动态颜色编码：性能等级用不同颜色区分

**场景趋势分析** ✅：
- ✅ X轴：场景名称（支持原始文件名），Y轴：指标值(0-100%)
- ✅ 距离段切换：每个距离段显示6条线(3类别×2指标)
- ✅ 支持大量场景：已测试50个场景的显示
- ✅ 折线图样式：直线连接，无数据点标记

**其他要求** ✅：
- ✅ 多页面结构：主页（概览）+ 50个场景子页 + 导航页面
- ✅ 纯静态产物：52个HTML文件，完全离线可访问

### 3.4 配置与易用性
1. 命令行入口 `python generate_report.py --input data/ --output report/`。
2. 可选 YAML/JSON 配置文件（距离分桶、类别中文名、主题颜色）。
3. 输出目录若存在可选择覆盖或版本号区分。
4. 支持一键打包为 ZIP 供邮件或 IM 发送。

### 3.5 视觉设计要求 ✅

**柔和扁平化风格** ✅：
- ✅ 背景：渐变背景 (linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%))
- ✅ 卡片：毛玻璃效果 (rgba(255, 255, 255, 0.9) + backdrop-filter: blur(10px))
- ✅ 文字：温和蓝灰色调 (#34495e, #5a6c7d, #7f8c8d)
- ✅ 强调色：柔和配色，避免刺眼对比

**颜色方案** ✅：
- ✅ 人：蓝色系 (#1f77b4, #aec7e8)
- ✅ 车：绿色系 (#2ca02c, #98df8a)
- ✅ 非机动车：橙色系 (#ff7f0e, #ffbb78)
- ✅ 准确率：实线，检出率：虚线
- ✅ 性能等级：绿色>80%，蓝色60%-80%，橙色<60%（替代刺眼红色）

## 4. 非功能需求 ✅
- ✅ **可移植性**：纯 HTML + JS，无需服务器即可打开。
- ✅ **性能**：大规模数据量（21,120行）生成时间 < 5分钟，浏览器渲染 < 3秒。
- ✅ **可维护性**：模块化 Python 代码，函数单一职责，配有文档字符串。
- ✅ **兼容性**：兼容 Chrome 90+、Edge 90+、Firefox 88+。
- ✅ **扩展性**：已验证从2个场景扩展到50个场景的能力。

## 5. 约束条件 ✅
- ✅ 使用 **ECharts** 原生JavaScript库（替代pyecharts以获得更好的定制性）。
- ✅ 仅依赖纯 Python 环境（无需 Node）。
- ✅ Excel 文件结构需符合字段规范；异常将打印警告并跳过。
- ✅ 支持复杂Excel格式：多工作表、表头行、混合数据类型。

## 6. 风险与假设
1. Excel 字段不统一 → 通过配置映射解决。
2. 数据量过大导致浏览器卡顿 → 启用 ECharts `large` 模式或分层采样。
3. 领导对配色/样式有特殊要求 → 预留自定义主题入口。

## 7. 交付物 ✅
1. ✅ 源代码（Git）：完整的模块化Python代码。
2. ✅ `requirements.txt`、`README.md`：完整的环境配置和使用说明。
3. ✅ 静态报告示例：50个场景的完整报告（52个HTML文件）。
4. ✅ 本需求说明书：更新版本，反映所有已实现功能。
5. ✅ 测试数据生成器：可生成任意数量场景的测试数据。

## 8. 新增功能实现 ✅

### 8.1 大规模数据处理能力 ✅
- ✅ 50个场景并行处理
- ✅ 21,120行原始数据处理
- ✅ 6种维度数据聚合
- ✅ 内存优化和性能优化

### 8.2 完整时间序列分析 ✅
- ✅ 79个连续时间戳的趋势分析
- ✅ 三距离段时间趋势（0-50米、50-100米、总体）
- ✅ 多类别时间序列对比
- ✅ 双指标时间趋势（检出率、准确率）

### 8.3 柔和扁平化UI设计 ✅
- ✅ 渐变背景替代纯色背景
- ✅ 毛玻璃卡片效果
- ✅ 温和色彩搭配，避免刺眼对比
- ✅ 现代化交互动画

### 8.4 系统扩展性验证 ✅
- ✅ 场景数量扩展性（2→50个场景）
- ✅ 数据量扩展性（474→21,120行数据）
- ✅ 时间序列扩展性（79个时间戳）
- ✅ 图表类型扩展性（4种图表类型）

---
