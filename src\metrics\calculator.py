"""
指标计算模块
基于TP/FP/FN计算recall、precision、f1等派生指标，生成聚合数据表用于可视化
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MetricsCalculator:
    """指标计算器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化指标计算器
        
        Args:
            config: 配置字典
        """
        self.config = config
        
    def calculate_basic_metrics(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算基础指标：recall, precision, f1
        
        Args:
            df: 包含tp, fp, fn列的DataFrame
            
        Returns:
            添加了指标列的DataFrame
        """
        logger.info("开始计算基础指标...")
        
        df = df.copy()
        
        # 计算recall (检出率) = TP / (TP + FN)
        df['recall'] = np.where(
            (df['tp'] + df['fn']) > 0,
            df['tp'] / (df['tp'] + df['fn']),
            0
        )
        
        # 计算precision (准确率) = TP / (TP + FP)
        df['precision'] = np.where(
            (df['tp'] + df['fp']) > 0,
            df['tp'] / (df['tp'] + df['fp']),
            0
        )
        
        # 计算F1分数 = 2 * (precision * recall) / (precision + recall)
        df['f1'] = np.where(
            (df['precision'] + df['recall']) > 0,
            2 * (df['precision'] * df['recall']) / (df['precision'] + df['recall']),
            0
        )
        
        # 计算样本数
        df['sample_cnt'] = df['tp'] + df['fn']
        
        logger.info("基础指标计算完成")
        return df
    
    def aggregate_by_scene_distance_category(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        按场景、距离段、类别聚合数据
        
        Args:
            df: 原始数据DataFrame
            
        Returns:
            聚合后的DataFrame
        """
        logger.info("开始按场景-距离-类别聚合数据...")
        
        # 聚合函数
        agg_funcs = {
            'tp': 'sum',
            'fp': 'sum', 
            'fn': 'sum',
            'true_count': 'sum',
            'detected_count': 'sum',
            'false_count': 'sum',
            'sample_cnt': 'sum'
        }
        
        # 按场景、距离段、类别分组聚合
        grouped = df.groupby(['scene_id', 'scene_name', 'distance_bin', 'target_cls', 'target_cls_name']).agg(agg_funcs).reset_index()

        # 重新计算聚合后的指标
        grouped = self.calculate_basic_metrics(grouped)

        # 添加"总体"距离段数据
        logger.info("计算总体距离段数据...")
        overall_data = df.groupby(['scene_id', 'scene_name', 'target_cls', 'target_cls_name']).agg(agg_funcs).reset_index()

        # 为总体数据添加distance_bin列
        overall_data['distance_bin'] = '总体'

        # 重新计算总体数据的指标
        overall_data = self.calculate_basic_metrics(overall_data)

        # 合并原始聚合数据和总体数据
        final_grouped = pd.concat([grouped, overall_data], ignore_index=True)

        logger.info(f"聚合完成，得到 {len(final_grouped)} 行数据（包含总体距离段）")
        return final_grouped
    
    def aggregate_by_scene(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        按场景聚合数据（用于全场景对比）
        
        Args:
            df: 原始数据DataFrame
            
        Returns:
            按场景聚合的DataFrame
        """
        logger.info("开始按场景聚合数据...")
        
        agg_funcs = {
            'tp': 'sum',
            'fp': 'sum',
            'fn': 'sum',
            'sample_cnt': 'sum'
        }
        
        # 按场景分组聚合
        scene_agg = df.groupby(['scene_id', 'scene_name']).agg(agg_funcs).reset_index()
        
        # 重新计算指标
        scene_agg = self.calculate_basic_metrics(scene_agg)
        
        logger.info(f"场景聚合完成，得到 {len(scene_agg)} 个场景")
        return scene_agg
    
    def aggregate_by_scene_distance(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        按场景和距离段聚合数据（用于热力图）
        
        Args:
            df: 原始数据DataFrame
            
        Returns:
            按场景-距离聚合的DataFrame
        """
        logger.info("开始按场景-距离聚合数据...")
        
        agg_funcs = {
            'tp': 'sum',
            'fp': 'sum',
            'fn': 'sum',
            'sample_cnt': 'sum'
        }
        
        # 按场景和距离段分组聚合
        scene_dist_agg = df.groupby(['scene_id', 'scene_name', 'distance_bin']).agg(agg_funcs).reset_index()
        
        # 重新计算指标
        scene_dist_agg = self.calculate_basic_metrics(scene_dist_agg)
        
        logger.info(f"场景-距离聚合完成，得到 {len(scene_dist_agg)} 行数据")
        return scene_dist_agg
    
    def aggregate_by_scene_category(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        按场景和类别聚合数据（用于气泡图）
        
        Args:
            df: 原始数据DataFrame
            
        Returns:
            按场景-类别聚合的DataFrame
        """
        logger.info("开始按场景-类别聚合数据...")
        
        agg_funcs = {
            'tp': 'sum',
            'fp': 'sum',
            'fn': 'sum',
            'sample_cnt': 'sum'
        }
        
        # 按场景和类别分组聚合
        scene_cat_agg = df.groupby(['scene_id', 'scene_name', 'target_cls', 'target_cls_name']).agg(agg_funcs).reset_index()
        
        # 重新计算指标
        scene_cat_agg = self.calculate_basic_metrics(scene_cat_agg)
        
        logger.info(f"场景-类别聚合完成，得到 {len(scene_cat_agg)} 行数据")
        return scene_cat_agg
    
    def aggregate_by_distance_category(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        按距离段和类别聚合数据（用于箱线图）
        
        Args:
            df: 原始数据DataFrame
            
        Returns:
            按距离-类别聚合的DataFrame
        """
        logger.info("开始按距离-类别聚合数据...")
        
        agg_funcs = {
            'tp': 'sum',
            'fp': 'sum',
            'fn': 'sum',
            'sample_cnt': 'sum'
        }
        
        # 按距离段和类别分组聚合
        dist_cat_agg = df.groupby(['distance_bin', 'target_cls', 'target_cls_name']).agg(agg_funcs).reset_index()
        
        # 重新计算指标
        dist_cat_agg = self.calculate_basic_metrics(dist_cat_agg)
        
        logger.info(f"距离-类别聚合完成，得到 {len(dist_cat_agg)} 行数据")
        return dist_cat_agg
    
    def generate_summary_stats(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        生成汇总统计信息
        
        Args:
            df: 数据DataFrame
            
        Returns:
            统计信息字典
        """
        logger.info("生成汇总统计信息...")
        
        stats = {
            'total_scenes': df['scene_id'].nunique(),
            'total_distance_bins': df['distance_bin'].nunique(),
            'total_categories': df['target_cls'].nunique(),
            'total_samples': df['sample_cnt'].sum(),
            'total_tp': df['tp'].sum(),
            'total_fp': df['fp'].sum(),
            'total_fn': df['fn'].sum(),
            'overall_recall': df['tp'].sum() / (df['tp'].sum() + df['fn'].sum()) if (df['tp'].sum() + df['fn'].sum()) > 0 else 0,
            'overall_precision': df['tp'].sum() / (df['tp'].sum() + df['fp'].sum()) if (df['tp'].sum() + df['fp'].sum()) > 0 else 0,
        }
        
        # 计算总体F1分数
        if (stats['overall_precision'] + stats['overall_recall']) > 0:
            stats['overall_f1'] = 2 * (stats['overall_precision'] * stats['overall_recall']) / (stats['overall_precision'] + stats['overall_recall'])
        else:
            stats['overall_f1'] = 0
            
        # 按场景统计
        scene_stats = df.groupby('scene_name').agg({
            'recall': 'mean',
            'precision': 'mean',
            'f1': 'mean',
            'sample_cnt': 'sum'
        }).to_dict('index')
        stats['by_scene'] = scene_stats
        
        # 按类别统计
        category_stats = df.groupby('target_cls_name').agg({
            'recall': 'mean',
            'precision': 'mean',
            'f1': 'mean',
            'sample_cnt': 'sum'
        }).to_dict('index')
        stats['by_category'] = category_stats
        
        # 按距离段统计
        distance_stats = df.groupby('distance_bin').agg({
            'recall': 'mean',
            'precision': 'mean',
            'f1': 'mean',
            'sample_cnt': 'sum'
        }).to_dict('index')
        stats['by_distance'] = distance_stats
        
        logger.info("汇总统计信息生成完成")
        return stats
    
    def process_all_aggregations(self, df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        处理所有聚合操作
        
        Args:
            df: 原始数据DataFrame
            
        Returns:
            包含所有聚合结果的字典
        """
        logger.info("开始处理所有聚合操作...")
        
        # 计算基础指标
        df_with_metrics = self.calculate_basic_metrics(df)
        
        # 执行各种聚合
        results = {
            'raw_data': df_with_metrics,
            'scene_distance_category': self.aggregate_by_scene_distance_category(df_with_metrics),
            'by_scene': self.aggregate_by_scene(df_with_metrics),
            'by_scene_distance': self.aggregate_by_scene_distance(df_with_metrics),
            'by_scene_category': self.aggregate_by_scene_category(df_with_metrics),
            'by_distance_category': self.aggregate_by_distance_category(df_with_metrics),
        }
        
        # 生成汇总统计
        results['summary_stats'] = self.generate_summary_stats(df_with_metrics)
        
        logger.info("所有聚合操作完成")
        return results


if __name__ == "__main__":
    # 测试代码
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'loader'))
    
    from excel_loader import ExcelDataLoader, load_config
    
    # 加载配置和数据
    config_path = "../../config/config.yaml"
    data_dir = "../../data"
    
    config = load_config(config_path)
    loader = ExcelDataLoader(config)
    calculator = MetricsCalculator(config)
    
    try:
        # 加载数据
        df = loader.load_excel_files(data_dir)
        df = loader.clean_and_validate_data(df)
        
        # 处理所有聚合
        results = calculator.process_all_aggregations(df)
        
        print("指标计算成功!")
        print(f"原始数据: {results['raw_data'].shape}")
        print(f"场景-距离-类别聚合: {results['scene_distance_category'].shape}")
        print(f"场景聚合: {results['by_scene'].shape}")
        print(f"场景-距离聚合: {results['by_scene_distance'].shape}")
        print(f"场景-类别聚合: {results['by_scene_category'].shape}")
        print(f"距离-类别聚合: {results['by_distance_category'].shape}")
        
        print("\n汇总统计:")
        stats = results['summary_stats']
        print(f"总场景数: {stats['total_scenes']}")
        print(f"总距离段数: {stats['total_distance_bins']}")
        print(f"总类别数: {stats['total_categories']}")
        print(f"总样本数: {stats['total_samples']}")
        print(f"总体检出率: {stats['overall_recall']:.3f}")
        print(f"总体准确率: {stats['overall_precision']:.3f}")
        print(f"总体F1分数: {stats['overall_f1']:.3f}")
        
    except Exception as e:
        print(f"指标计算失败: {e}")
        import traceback
        traceback.print_exc()
