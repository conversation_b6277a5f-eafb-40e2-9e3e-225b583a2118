"""
全场景图表模块 - 商务科技风格
实现卡片风格概览、场景趋势图+切换
"""

import pandas as pd
import numpy as np
import json
from typing import Dict, List, Any, Optional
from pyecharts import options as opts
from pyecharts.charts import Line, Page
from pyecharts.globals import ThemeType
import logging

try:
    from .theme import ChartTheme
except ImportError:
    from theme import ChartTheme

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MasterSceneCharts:
    """全场景图表生成器 - 商务科技风格"""

    def __init__(self, theme: ChartTheme):
        """
        初始化图表生成器

        Args:
            theme: 图表主题配置
        """
        self.theme = theme
        self.color_palette = theme.get_color_palette()
        self.category_colors = theme.get_category_colors()
        self.metric_colors = theme.get_metric_colors()
        
    def convert_numpy_types(self, obj):
        """递归转换numpy数据类型为原生Python类型"""
        if isinstance(obj, dict):
            return {key: self.convert_numpy_types(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self.convert_numpy_types(item) for item in obj]
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        else:
            return obj

    def create_overview_cards(self, scene_data: pd.DataFrame) -> str:
        """
        创建卡片风格的总体概览

        Args:
            scene_data: 按场景聚合的数据

        Returns:
            HTML字符串
        """
        logger.info("创建全场景概览卡片")

        # 计算所有场景的平均指标
        avg_recall = scene_data['recall'].mean()
        avg_precision = scene_data['precision'].mean()
        avg_f1 = scene_data['f1'].mean()
        total_scenes = scene_data['scene_id'].nunique()

        # 卡片样式
        card_style = self.theme.get_kpi_card_style()

        html_content = f"""
        <style>
            .summary-cards {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 25px;
                margin: 20px 0;
            }}

            .summary-card {{
                background: rgba(255, 255, 255, 0.95);
                border-radius: 16px;
                padding: 25px;
                box-shadow: 0 6px 20px rgba(0,0,0,0.06);
                border-left: 4px solid;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
                backdrop-filter: blur(10px);
            }}

            .summary-card::before {{
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                width: 80px;
                height: 80px;
                background: linear-gradient(45deg, rgba(255,255,255,0.3), transparent);
                border-radius: 50%;
                transform: translate(25px, -25px);
            }}

            .summary-card:hover {{
                transform: translateY(-3px);
                box-shadow: 0 12px 30px rgba(0,0,0,0.1);
            }}

            .summary-card.excellent {{ border-left-color: #2ecc71; }}
            .summary-card.good {{ border-left-color: #3498db; }}
            .summary-card.needs-improvement {{ border-left-color: #e67e22; }}
            .summary-card.overall {{ border-left-color: #9b59b6; }}

            .card-header {{
                display: flex;
                align-items: center;
                margin-bottom: 15px;
            }}

            .card-icon {{
                font-size: 2.5rem;
                margin-right: 15px;
            }}

            .card-title {{
                font-size: 1.2rem;
                font-weight: 500;
                color: #34495e;
            }}

            .card-metrics {{
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 15px;
                margin-top: 20px;
            }}

            .metric {{
                text-align: center;
            }}

            .metric-value {{
                font-size: 1.8rem;
                font-weight: 600;
                margin-bottom: 5px;
            }}

            .metric-label {{
                font-size: 0.85rem;
                color: #95a5a6;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                font-weight: 500;
            }}
        </style>

        <div class="summary-cards">
            <div class="summary-card {'excellent' if avg_recall >= 0.8 else 'good' if avg_recall >= 0.6 else 'needs-improvement'}">
                <div class="card-header">
                    <div class="card-icon">🎯</div>
                    <div class="card-title">总体检出率</div>
                </div>
                <div class="card-metrics">
                    <div class="metric">
                        <div class="metric-value" style="color: {'#2ecc71' if avg_recall >= 0.8 else '#3498db' if avg_recall >= 0.6 else '#e67e22'};">{avg_recall:.1%}</div>
                        <div class="metric-label">平均值</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" style="color: #7f8c8d;">{total_scenes}</div>
                        <div class="metric-label">场景数</div>
                    </div>
                </div>
            </div>

            <div class="summary-card {'excellent' if avg_precision >= 0.8 else 'good' if avg_precision >= 0.6 else 'needs-improvement'}">
                <div class="card-header">
                    <div class="card-icon">✅</div>
                    <div class="card-title">总体准确率</div>
                </div>
                <div class="card-metrics">
                    <div class="metric">
                        <div class="metric-value" style="color: {'#2ecc71' if avg_precision >= 0.8 else '#3498db' if avg_precision >= 0.6 else '#e67e22'};">{avg_precision:.1%}</div>
                        <div class="metric-label">平均值</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" style="color: #7f8c8d;">{total_scenes}</div>
                        <div class="metric-label">场景数</div>
                    </div>
                </div>
            </div>

            <div class="summary-card {'excellent' if avg_f1 >= 0.8 else 'good' if avg_f1 >= 0.6 else 'needs-improvement'}">
                <div class="card-header">
                    <div class="card-icon">⚖️</div>
                    <div class="card-title">总体F1分数</div>
                </div>
                <div class="card-metrics">
                    <div class="metric">
                        <div class="metric-value" style="color: {'#2ecc71' if avg_f1 >= 0.8 else '#3498db' if avg_f1 >= 0.6 else '#e67e22'};">{avg_f1:.1%}</div>
                        <div class="metric-label">平均值</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" style="color: #7f8c8d;">{total_scenes}</div>
                        <div class="metric-label">场景数</div>
                    </div>
                </div>
            </div>

            <div class="summary-card overall">
                <div class="card-header">
                    <div class="card-icon">📊</div>
                    <div class="card-title">分析统计</div>
                </div>
                <div class="card-metrics">
                    <div class="metric">
                        <div class="metric-value" style="color: #9b59b6;">{total_scenes}</div>
                        <div class="metric-label">场景总数</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" style="color: #9b59b6;">3</div>
                        <div class="metric-label">目标类别</div>
                    </div>
                </div>
            </div>
        </div>
        """

        return html_content

    def create_category_overview_cards(self, scene_category_data: pd.DataFrame) -> str:
        """
        创建分类别概览卡片

        Args:
            scene_category_data: 按场景-类别聚合的数据

        Returns:
            HTML字符串
        """
        logger.info("创建分类别概览卡片")

        # 按类别计算平均指标
        category_stats = scene_category_data.groupby('target_cls_name').agg({
            'recall': 'mean',
            'precision': 'mean'
        }).reset_index()

        # 卡片样式
        card_style = self.theme.get_kpi_card_style()

        # 获取类别图标映射
        category_icons = {
            '行人': '🚶',
            '机动车': '🚗',
            '非机动车': '🚲'
        }

        html_content = f"""
        <div class="summary-cards">
        """

        # 为每个类别创建卡片
        for _, row in category_stats.iterrows():
            category = row['target_cls_name']
            recall = row['recall']
            precision = row['precision']

            # 获取类别图标
            icon = category_icons.get(category, '📊')

            # 根据性能确定卡片类型
            avg_performance = (recall + precision) / 2
            card_class = 'excellent' if avg_performance >= 0.8 else 'good' if avg_performance >= 0.6 else 'needs-improvement'

            html_content += f"""
            <div class="summary-card {card_class}">
                <div class="card-header">
                    <div class="card-icon">{icon}</div>
                    <div class="card-title">{category}检测</div>
                </div>
                <div class="card-metrics">
                    <div class="metric">
                        <div class="metric-value" style="color: {'#2ecc71' if recall >= 0.8 else '#3498db' if recall >= 0.6 else '#e67e22'};">{recall:.1%}</div>
                        <div class="metric-label">检出率</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" style="color: {'#2ecc71' if precision >= 0.8 else '#3498db' if precision >= 0.6 else '#e67e22'};">{precision:.1%}</div>
                        <div class="metric-label">准确率</div>
                    </div>
                </div>
            </div>
            """

        html_content += """
        </div>
        """

        return html_content

    def create_scene_trend_with_switch(self, scene_distance_category_data: pd.DataFrame) -> str:
        """
        创建场景趋势图+切换按钮的HTML组合

        Args:
            scene_distance_category_data: 按场景-距离-类别聚合的数据

        Returns:
            包含趋势图和切换按钮的HTML字符串
        """
        logger.info("创建全场景趋势图+切换")

        # 按距离段和场景聚合数据
        trend_data = scene_distance_category_data.groupby(['scene_name', 'distance_bin', 'target_cls_name']).agg({
            'recall': 'mean',
            'precision': 'mean'
        }).reset_index()

        # 获取距离段、场景和类别列表
        distance_bins = sorted(trend_data['distance_bin'].unique())
        scenes = sorted(trend_data['scene_name'].unique())
        categories = sorted(trend_data['target_cls_name'].unique())

        # 为每个距离段准备数据
        trend_charts_data = {}

        for dist_bin in distance_bins:
            dist_data = trend_data[trend_data['distance_bin'] == dist_bin]

            series_data = {}
            for category in categories:
                cat_data = dist_data[dist_data['target_cls_name'] == category]

                # 准确率场景序列
                precision_series = []
                recall_series = []

                for scene in scenes:
                    scene_data = cat_data[cat_data['scene_name'] == scene]
                    if not scene_data.empty:
                        precision_series.append(round(scene_data['precision'].iloc[0] * 100, 1))
                        recall_series.append(round(scene_data['recall'].iloc[0] * 100, 1))
                    else:
                        precision_series.append(0)
                        recall_series.append(0)

                series_data[f"{category}-准确率"] = precision_series
                series_data[f"{category}-检出率"] = recall_series

            trend_charts_data[dist_bin] = series_data

        # 生成HTML
        html_content = f"""
        <div id="master-trend-container" style="width: 100%; height: 700px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: {self.theme.chart_theme.get('text_color', '#ffffff')}; margin-bottom: 15px;">
                    全场景趋势分析
                </h3>
                <div id="master-trend-buttons" style="margin-bottom: 20px;">
        """

        # 添加切换按钮
        for i, dist_bin in enumerate(distance_bins):
            active_class = "master-trend-active" if i == 0 else ""
            html_content += f"""
                    <button onclick="switchMasterTrend('{dist_bin}')"
                            class="master-trend-btn {active_class}"
                            style="margin: 0 10px; padding: 8px 16px;
                                   background: {self.theme.chart_theme.get('secondary_bg', '#2d2d2d')};
                                   color: {self.theme.chart_theme.get('text_color', '#ffffff')};
                                   border: 1px solid {self.theme.chart_theme.get('grid_color', '#404040')};
                                   border-radius: 4px; cursor: pointer;">
                        {dist_bin}
                    </button>
            """

        html_content += f"""
                </div>
            </div>
            <div id="master-trend-chart" style="width: 100%; height: 600px;"></div>
        </div>

        <script>
            // 场景名称数据
            var masterScenes = {json.dumps(self.convert_numpy_types(scenes), ensure_ascii=False)};

            // 趋势图数据
            var masterTrendData = {json.dumps(self.convert_numpy_types(trend_charts_data), ensure_ascii=False)};

            // 颜色配置
            var masterCategoryColors = {{
                '行人': ['{self.category_colors.get('行人', {}).get('primary', '#1f77b4')}', '{self.category_colors.get('行人', {}).get('secondary', '#aec7e8')}'],
                '机动车': ['{self.category_colors.get('机动车', {}).get('primary', '#2ca02c')}', '{self.category_colors.get('机动车', {}).get('secondary', '#98df8a')}'],
                '非机动车': ['{self.category_colors.get('非机动车', {}).get('primary', '#ff7f0e')}', '{self.category_colors.get('非机动车', {}).get('secondary', '#ffbb78')}']
            }};

            // 趋势图配置
            function getMasterTrendOption(distBin) {{
                var seriesData = masterTrendData[distBin];
                var series = [];

                Object.keys(seriesData).forEach(function(seriesName) {{
                    var category = seriesName.split('-')[0];
                    var metric = seriesName.split('-')[1];
                    var colorIndex = metric === '准确率' ? 0 : 1;
                    var lineType = metric === '准确率' ? 'solid' : 'dashed';

                    series.push({{
                        name: seriesName,
                        type: 'line',
                        data: seriesData[seriesName],
                        smooth: false,
                        symbol: 'none',
                        lineStyle: {{
                            color: masterCategoryColors[category][colorIndex],
                            width: 3,
                            type: lineType
                        }},
                        emphasis: {{
                            focus: 'series',
                            lineStyle: {{
                                width: 4
                            }}
                        }}
                    }});
                }});

                return {{
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    title: {{
                        text: distBin + ' - 场景性能趋势',
                        left: 'center',
                        textStyle: {{
                            color: '#5a6c7d',
                            fontSize: 16
                        }}
                    }},
                    tooltip: {{
                        trigger: 'axis',
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        borderColor: '#bdc3c7',
                        textStyle: {{
                            color: '#2c3e50'
                        }}
                    }},
                    legend: {{
                        top: '8%',
                        textStyle: {{
                            color: '#5a6c7d'
                        }}
                    }},
                    grid: {{
                        left: '10%',
                        right: '10%',
                        top: '20%',
                        bottom: '15%',
                        containLabel: true
                    }},
                    xAxis: {{
                        type: 'category',
                        data: masterScenes,
                        name: '场景',
                        axisLabel: {{
                            color: '#7f8c8d',
                            rotate: 45
                        }},
                        axisLine: {{
                            lineStyle: {{
                                color: '#bdc3c7'
                            }}
                        }}
                    }},
                    yAxis: {{
                        type: 'value',
                        name: '性能指标 (%)',
                        min: 0,
                        max: 100,
                        axisLabel: {{
                            color: '#7f8c8d'
                        }},
                        axisLine: {{
                            lineStyle: {{
                                color: '#bdc3c7'
                            }}
                        }},
                        splitLine: {{
                            lineStyle: {{
                                color: '#ecf0f1',
                                type: 'dashed'
                            }}
                        }}
                    }},
                    series: series
                }};
            }}

            // 初始化趋势图
            var masterTrendChart = echarts.init(document.getElementById('master-trend-chart'));
            masterTrendChart.setOption(getMasterTrendOption('{distance_bins[0]}'));

            // 切换函数
            function switchMasterTrend(distBin) {{
                // 更新按钮状态
                var buttons = document.querySelectorAll('#master-trend-buttons .master-trend-btn');
                buttons.forEach(btn => btn.classList.remove('master-trend-active'));
                event.target.classList.add('master-trend-active');

                // 更新趋势图
                masterTrendChart.setOption(getMasterTrendOption(distBin));
            }}
        </script>

        <style>
            .master-trend-btn.master-trend-active {{
                background: {self.theme.chart_theme.get('accent_color', '#00d4ff')} !important;
                color: #000000 !important;
            }}
            .master-trend-btn:hover {{
                background: {self.theme.chart_theme.get('accent_color', '#00d4ff')} !important;
                color: #000000 !important;
            }}
        </style>
        """

        return html_content

    def generate_master_page(self, results: Dict[str, pd.DataFrame]) -> str:
        """
        生成全场景总览页面HTML

        Args:
            results: 包含所有聚合结果的字典

        Returns:
            HTML字符串
        """
        logger.info("生成全场景总览页面")

        # 创建各种HTML组件
        overview_cards = self.create_overview_cards(results['by_scene'])
        category_cards = self.create_category_overview_cards(results['by_scene_category'])
        trend_html = self.create_scene_trend_with_switch(results['scene_distance_category'])

        # 生成完整HTML页面
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>全场景性能分析报告</title>
            <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
            <style>
                body {{
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    color: #2c3e50;
                    font-family: 'Segoe UI', 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
                    margin: 0;
                    padding: 20px;
                    min-height: 100vh;
                }}
                .section {{
                    margin: 30px 0;
                    padding: 25px;
                    background: rgba(255, 255, 255, 0.9);
                    border-radius: 12px;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                }}
                h1 {{
                    text-align: center;
                    color: #34495e;
                    font-weight: 300;
                    font-size: 2.5rem;
                    margin-bottom: 10px;
                    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }}
                h2 {{
                    text-align: center;
                    color: #5a6c7d;
                    font-weight: 400;
                    font-size: 1.8rem;
                    margin-bottom: 20px;
                }}
            </style>
        </head>
        <body>
            <h1>全场景性能分析报告</h1>

            <div class="section">
                <h2>总体概览</h2>
                {overview_cards}
            </div>

            <div class="section">
                <h2>分类别概览</h2>
                {category_cards}
            </div>

            <div class="section">
                <h2>场景趋势分析</h2>
                {trend_html}
            </div>
        </body>
        </html>
        """

        return html_content


if __name__ == "__main__":
    # 测试代码
    from theme import create_theme
    import pandas as pd

    # 测试主题和图表创建
    config = {'color_palette': {}, 'chart_theme': {}}
    theme = create_theme(config)
    charts = MasterSceneCharts(theme)

    # 创建测试数据
    test_scene_data = pd.DataFrame({
        'scene_id': ['scene1', 'scene2'],
        'scene_name': ['场景1', '场景2'],
        'recall': [0.85, 0.78],
        'precision': [0.82, 0.75],
        'f1': [0.835, 0.765]
    })

    test_scene_category_data = pd.DataFrame({
        'scene_name': ['场景1', '场景1', '场景2', '场景2'],
        'target_cls_name': ['行人', '机动车', '行人', '机动车'],
        'recall': [0.9, 0.8, 0.85, 0.75],
        'precision': [0.85, 0.82, 0.8, 0.7]
    })

    test_scene_distance_category_data = pd.DataFrame({
        'scene_name': ['场景1', '场景1', '场景2', '场景2'],
        'distance_bin': ['0-50米', '50-100米', '0-50米', '50-100米'],
        'target_cls_name': ['行人', '行人', '行人', '行人'],
        'recall': [0.9, 0.8, 0.85, 0.75],
        'precision': [0.85, 0.82, 0.8, 0.7]
    })

    print('创建概览卡片测试...')
    overview_cards = charts.create_overview_cards(test_scene_data)
    print(f'概览卡片创建成功，长度: {len(overview_cards)}')

    print('创建分类别概览卡片测试...')
    category_cards = charts.create_category_overview_cards(test_scene_category_data)
    print(f'分类别卡片创建成功，长度: {len(category_cards)}')

    print('创建场景趋势图HTML测试...')
    trend_html = charts.create_scene_trend_with_switch(test_scene_distance_category_data)
    print(f'场景趋势图HTML创建成功，长度: {len(trend_html)}')

    print('生成全场景页面测试...')
    results = {
        'by_scene': test_scene_data,
        'by_scene_category': test_scene_category_data,
        'scene_distance_category': test_scene_distance_category_data
    }
    master_html = charts.generate_master_page(results)
    print(f'全场景页面创建成功，长度: {len(master_html)}')

    print('所有全场景图表测试通过!')
