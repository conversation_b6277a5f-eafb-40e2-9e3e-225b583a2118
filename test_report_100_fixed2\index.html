
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>全场景性能分析报告</title>
            <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
            <style>
                body {
                    background: #1e1e1e;
                    color: #ffffff;
                    font-family: Microsoft YaHei, 'Segoe UI', Arial, sans-serif;
                    margin: 0;
                    padding: 20px;
                    min-height: 100vh;
                }
                .section {
                    margin: 30px 0;
                    padding: 25px;
                    background: #2d2d2d;
                    border-radius: 12px;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                    border: 1px solid #404040;
                }
                h1 {
                    text-align: center;
                    color: #ffffff;
                    font-weight: 300;
                    font-size: 2.5rem;
                    margin-bottom: 10px;
                    text-shadow: 0 2px 4px rgba(0,0,0,0.5);
                }
                h2 {
                    text-align: center;
                    color: #cccccc;
                    font-weight: 400;
                    font-size: 1.8rem;
                    margin-bottom: 20px;
                }
            </style>
        </head>
        <body>
            <h1>全场景性能分析报告</h1>

            <div class="section">
                <h2>总体概览</h2>
                
        <style>
            .summary-cards {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 25px;
                margin: 20px 0;
            }

            .summary-card {
                background: #2d2d2d;
                border-radius: 16px;
                padding: 25px;
                box-shadow: 0 6px 20px rgba(0,0,0,0.3);
                border-left: 4px solid;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
                border: 1px solid #404040;
            }

            .summary-card::before {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                width: 80px;
                height: 80px;
                background: linear-gradient(45deg, rgba(0,212,255,0.1), transparent);
                border-radius: 50%;
                transform: translate(25px, -25px);
            }

            .summary-card:hover {
                transform: translateY(-3px);
                box-shadow: 0 12px 30px rgba(0,0,0,0.4);
            }

            .summary-card.excellent { border-left-color: #2ecc71; }
            .summary-card.good { border-left-color: #3498db; }
            .summary-card.needs-improvement { border-left-color: #e67e22; }
            .summary-card.overall { border-left-color: #9b59b6; }

            .card-header {
                display: flex;
                align-items: center;
                margin-bottom: 15px;
            }

            .card-icon {
                font-size: 2.5rem;
                margin-right: 15px;
            }

            .card-title {
                font-size: 1.2rem;
                font-weight: 500;
                color: #ffffff;
            }

            .card-metrics {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 15px;
                margin-top: 20px;
            }

            .metric {
                text-align: center;
            }

            .metric-value {
                font-size: 1.8rem;
                font-weight: 600;
                margin-bottom: 5px;
            }

            .metric-label {
                font-size: 0.85rem;
                color: #cccccc;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                font-weight: 500;
            }
        </style>

        <div class="summary-cards">
            <div class="summary-card good">
                <div class="card-header">
                    <div class="card-icon">🎯</div>
                    <div class="card-title">总体检出率</div>
                </div>
                <div class="card-metrics">
                    <div class="metric">
                        <div class="metric-value" style="color: #3498db;">64.0%</div>
                        <div class="metric-label">平均值</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" style="color: #7f8c8d;">100</div>
                        <div class="metric-label">场景数</div>
                    </div>
                </div>
            </div>

            <div class="summary-card excellent">
                <div class="card-header">
                    <div class="card-icon">✅</div>
                    <div class="card-title">总体准确率</div>
                </div>
                <div class="card-metrics">
                    <div class="metric">
                        <div class="metric-value" style="color: #2ecc71;">82.9%</div>
                        <div class="metric-label">平均值</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" style="color: #7f8c8d;">100</div>
                        <div class="metric-label">场景数</div>
                    </div>
                </div>
            </div>

            <div class="summary-card good">
                <div class="card-header">
                    <div class="card-icon">⚖️</div>
                    <div class="card-title">总体F1分数</div>
                </div>
                <div class="card-metrics">
                    <div class="metric">
                        <div class="metric-value" style="color: #3498db;">72.3%</div>
                        <div class="metric-label">平均值</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" style="color: #7f8c8d;">100</div>
                        <div class="metric-label">场景数</div>
                    </div>
                </div>
            </div>

            <div class="summary-card overall">
                <div class="card-header">
                    <div class="card-icon">📊</div>
                    <div class="card-title">分析统计</div>
                </div>
                <div class="card-metrics">
                    <div class="metric">
                        <div class="metric-value" style="color: #9b59b6;">100</div>
                        <div class="metric-label">场景总数</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" style="color: #9b59b6;">3</div>
                        <div class="metric-label">目标类别</div>
                    </div>
                </div>
            </div>
        </div>
        
            </div>

            <div class="section">
                <h2>分类别概览</h2>
                
        <div class="summary-cards">
        
            <div class="summary-card good">
                <div class="card-header">
                    <div class="card-icon">🚗</div>
                    <div class="card-title">机动车检测</div>
                </div>
                <div class="card-metrics">
                    <div class="metric">
                        <div class="metric-value" style="color: #3498db;">62.3%</div>
                        <div class="metric-label">检出率</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" style="color: #2ecc71;">81.1%</div>
                        <div class="metric-label">准确率</div>
                    </div>
                </div>
            </div>
            
            <div class="summary-card excellent">
                <div class="card-header">
                    <div class="card-icon">🚶</div>
                    <div class="card-title">行人检测</div>
                </div>
                <div class="card-metrics">
                    <div class="metric">
                        <div class="metric-value" style="color: #3498db;">72.5%</div>
                        <div class="metric-label">检出率</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" style="color: #2ecc71;">91.7%</div>
                        <div class="metric-label">准确率</div>
                    </div>
                </div>
            </div>
            
            <div class="summary-card good">
                <div class="card-header">
                    <div class="card-icon">🚲</div>
                    <div class="card-title">非机动车检测</div>
                </div>
                <div class="card-metrics">
                    <div class="metric">
                        <div class="metric-value" style="color: #e67e22;">57.3%</div>
                        <div class="metric-label">检出率</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" style="color: #3498db;">75.7%</div>
                        <div class="metric-label">准确率</div>
                    </div>
                </div>
            </div>
            
        </div>
        
            </div>

            <div class="section">
                <h2>场景趋势分析</h2>
                
        <div id="master-trend-container" style="width: 100%; height: 700px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: #ffffff; margin-bottom: 15px;">
                    全场景趋势分析
                </h3>
                <div id="master-trend-buttons" style="margin-bottom: 20px;">
        
                    <button onclick="switchMasterTrend('0-50米', this)"
                            class="master-trend-btn master-trend-active"
                            style="margin: 0 10px; padding: 8px 16px;
                                   background: #2d2d2d;
                                   color: #ffffff;
                                   border: 1px solid #404040;
                                   border-radius: 4px; cursor: pointer;">
                        0-50米
                    </button>
            
                    <button onclick="switchMasterTrend('50-100米', this)"
                            class="master-trend-btn "
                            style="margin: 0 10px; padding: 8px 16px;
                                   background: #2d2d2d;
                                   color: #ffffff;
                                   border: 1px solid #404040;
                                   border-radius: 4px; cursor: pointer;">
                        50-100米
                    </button>
            
                    <button onclick="switchMasterTrend('总体', this)"
                            class="master-trend-btn "
                            style="margin: 0 10px; padding: 8px 16px;
                                   background: #2d2d2d;
                                   color: #ffffff;
                                   border: 1px solid #404040;
                                   border-radius: 4px; cursor: pointer;">
                        总体
                    </button>
            
                </div>
            </div>
            <div id="master-trend-chart" style="width: 100%; height: 600px;"></div>
        </div>

        <script>
            // 场景名称数据
            var masterScenes = ["deqing-001", "deqing-002", "deqing-003", "deqing-004", "deqing-005", "deqing-006", "deqing-007", "deqing-008", "deqing-009", "deqing-010", "deqing-011", "deqing-012", "deqing-013", "deqing-014", "deqing-015", "deqing-016", "deqing-017", "deqing-018", "deqing-019", "deqing-020", "deqing-021", "deqing-022", "deqing-023", "deqing-024", "deqing-025", "deqing-026", "deqing-027", "deqing-028", "deqing-029", "deqing-030", "deqing-031", "deqing-032", "deqing-033", "deqing-034", "deqing-035", "deqing-036", "deqing-037", "deqing-038", "deqing-039", "deqing-040", "deqing-041", "deqing-042", "deqing-043", "deqing-044", "deqing-045", "deqing-046", "deqing-047", "deqing-048", "deqing-049", "deqing-050", "deqing-051", "deqing-052", "deqing-053", "deqing-054", "deqing-055", "deqing-056", "deqing-057", "deqing-058", "deqing-059", "deqing-060", "deqing-061", "deqing-062", "deqing-063", "deqing-064", "deqing-065", "deqing-066", "deqing-067", "deqing-068", "deqing-069", "deqing-070", "deqing-071", "deqing-072", "deqing-073", "deqing-074", "deqing-075", "deqing-076", "deqing-077", "deqing-078", "deqing-079", "deqing-080", "deqing-081", "deqing-082", "deqing-083", "deqing-084", "deqing-085", "deqing-086", "deqing-087", "deqing-088", "deqing-089", "deqing-090", "deqing-091", "deqing-092", "deqing-093", "deqing-094", "deqing-095", "deqing-096", "deqing-097", "deqing-098", "deqing-099", "deqing-100"];

            // 趋势图数据
            var masterTrendData = {"0-50米": {"机动车-准确率": [91.3, 86.6, 86.6, 85.6, 84.9, 86.5, 85.8, 89.1, 86.8, 87.0, 86.9, 86.7, 89.9, 87.1, 85.3, 86.3, 87.5, 84.6, 86.1, 88.4, 87.8, 87.3, 86.9, 86.4, 83.8, 88.7, 87.3, 87.1, 87.8, 87.9, 86.2, 87.6, 89.0, 87.0, 84.9, 85.7, 86.6, 87.0, 86.9, 89.0, 91.2, 86.0, 85.4, 89.0, 86.9, 86.0, 87.8, 85.8, 84.4, 84.9, 87.4, 86.9, 85.9, 87.6, 88.1, 86.6, 84.8, 88.3, 85.0, 87.4, 88.9, 83.7, 88.7, 87.0, 87.1, 85.9, 89.0, 86.6, 83.7, 86.9, 83.5, 85.2, 86.6, 84.6, 85.8, 82.7, 87.4, 87.2, 84.1, 85.1, 87.5, 86.0, 87.6, 87.8, 86.8, 89.4, 85.3, 89.3, 86.1, 87.0, 85.8, 87.1, 85.7, 87.7, 86.2, 86.3, 84.9, 86.0, 86.8, 88.7], "机动车-检出率": [64.7, 69.6, 66.8, 67.9, 68.0, 68.3, 68.9, 69.7, 68.1, 69.7, 65.9, 65.1, 64.6, 69.6, 66.7, 67.4, 67.1, 65.5, 69.2, 67.9, 68.1, 68.1, 67.6, 66.4, 69.1, 69.1, 64.4, 66.7, 67.8, 66.7, 65.7, 67.1, 67.4, 67.7, 68.3, 68.0, 69.0, 66.7, 66.5, 66.1, 65.3, 67.2, 68.7, 66.9, 67.5, 70.0, 69.0, 69.3, 67.8, 66.9, 66.1, 68.9, 67.7, 67.2, 69.7, 68.4, 68.2, 66.8, 66.6, 66.2, 67.6, 66.8, 67.2, 67.8, 68.9, 66.0, 65.8, 67.3, 70.0, 67.8, 67.2, 66.8, 67.4, 65.9, 68.2, 67.7, 68.3, 67.2, 67.6, 67.9, 65.7, 67.3, 69.0, 65.6, 66.2, 66.7, 69.0, 64.5, 69.1, 65.7, 69.2, 66.8, 66.7, 69.4, 65.4, 70.5, 68.8, 66.7, 68.4, 67.9], "行人-准确率": [96.0, 95.9, 95.6, 96.1, 96.1, 96.8, 97.1, 96.2, 95.4, 95.5, 97.3, 97.8, 95.7, 97.5, 96.0, 95.2, 96.1, 96.9, 98.0, 96.5, 97.6, 96.6, 95.6, 96.0, 97.1, 95.7, 98.1, 96.3, 96.6, 97.2, 96.1, 97.4, 95.7, 97.8, 95.2, 95.6, 96.6, 96.0, 97.2, 96.4, 96.3, 96.6, 96.2, 96.8, 97.0, 97.5, 96.2, 95.6, 96.9, 94.7, 96.7, 96.2, 95.7, 96.8, 97.4, 97.3, 97.6, 96.1, 96.5, 96.1, 95.5, 96.5, 95.9, 97.7, 97.4, 96.3, 96.9, 97.7, 97.1, 95.5, 95.2, 95.8, 96.9, 95.0, 97.4, 96.0, 96.2, 96.5, 96.6, 96.4, 96.0, 96.0, 96.9, 96.6, 95.0, 95.8, 95.3, 96.6, 96.0, 96.6, 96.6, 97.6, 96.9, 96.3, 96.8, 95.3, 96.2, 95.4, 97.6, 96.7], "行人-检出率": [76.4, 78.4, 77.6, 76.9, 76.7, 76.6, 77.3, 75.6, 76.9, 78.7, 75.9, 76.4, 78.7, 75.4, 77.9, 78.5, 77.7, 76.9, 76.0, 77.0, 78.4, 78.4, 76.7, 78.5, 78.9, 77.4, 77.1, 78.6, 76.9, 76.3, 78.8, 77.8, 77.9, 75.5, 77.3, 77.5, 77.9, 77.9, 76.7, 76.4, 78.1, 79.0, 76.3, 78.3, 76.5, 77.2, 79.5, 77.6, 77.3, 77.9, 77.2, 77.0, 76.5, 77.7, 77.0, 78.4, 78.1, 77.0, 76.2, 77.6, 75.9, 77.1, 78.8, 77.0, 76.5, 79.8, 76.2, 78.3, 76.7, 76.9, 77.3, 78.1, 79.2, 77.3, 77.8, 76.3, 77.4, 77.3, 77.3, 76.9, 77.7, 75.8, 77.9, 77.2, 78.6, 78.5, 77.5, 77.4, 76.9, 77.9, 78.4, 75.8, 77.1, 77.6, 77.5, 77.2, 76.4, 76.1, 76.7, 76.2], "非机动车-准确率": [81.6, 82.1, 85.0, 82.2, 81.0, 81.9, 82.8, 82.3, 80.5, 82.6, 80.2, 81.9, 80.3, 83.0, 83.8, 81.0, 81.2, 79.5, 84.5, 80.2, 80.5, 82.7, 82.7, 85.9, 82.5, 80.9, 81.1, 82.4, 81.4, 81.4, 84.3, 80.8, 82.8, 82.5, 83.4, 77.5, 80.6, 82.8, 82.2, 81.8, 82.7, 82.5, 79.5, 82.0, 80.5, 83.9, 85.4, 78.4, 81.9, 82.1, 83.6, 82.5, 82.8, 79.8, 84.0, 80.0, 81.6, 81.1, 81.0, 81.1, 84.5, 82.7, 85.1, 80.4, 80.1, 81.3, 83.9, 83.9, 81.0, 83.2, 79.4, 79.9, 84.7, 82.1, 81.3, 85.3, 85.3, 81.6, 82.4, 79.6, 82.5, 81.4, 87.3, 79.2, 80.1, 79.1, 80.6, 82.3, 81.2, 82.5, 80.1, 82.3, 80.2, 80.4, 79.6, 80.8, 79.9, 82.1, 79.8, 82.5], "非机动车-检出率": [62.7, 63.1, 60.6, 62.3, 63.3, 63.1, 61.7, 61.3, 63.5, 61.8, 63.0, 60.2, 63.0, 61.8, 62.2, 62.5, 63.6, 63.5, 62.8, 63.9, 62.4, 62.0, 62.3, 61.2, 62.7, 60.9, 61.3, 63.0, 63.8, 62.3, 61.8, 61.2, 60.7, 61.6, 61.5, 64.6, 61.8, 61.4, 62.6, 64.0, 60.6, 60.2, 63.1, 61.4, 64.2, 62.4, 61.9, 64.8, 61.2, 61.9, 63.8, 61.2, 62.7, 60.9, 61.1, 63.1, 60.3, 61.5, 64.2, 62.7, 60.4, 61.6, 60.0, 63.6, 60.6, 63.8, 63.7, 61.0, 61.3, 60.1, 61.6, 62.1, 62.4, 60.8, 61.4, 61.8, 61.0, 64.1, 60.9, 62.3, 62.8, 60.5, 61.7, 63.3, 61.5, 62.7, 61.9, 62.1, 64.0, 62.7, 63.2, 61.8, 61.0, 59.5, 62.2, 62.6, 63.6, 63.5, 62.4, 61.9]}, "50-100米": {"机动车-准确率": [75.8, 75.7, 77.2, 77.5, 74.8, 74.3, 75.3, 77.0, 74.5, 74.4, 72.6, 77.4, 73.8, 74.1, 74.9, 71.8, 79.8, 76.1, 74.6, 72.7, 78.8, 77.2, 78.5, 77.6, 77.6, 75.6, 76.1, 75.6, 76.6, 74.4, 71.8, 76.0, 76.6, 72.2, 76.2, 82.4, 76.0, 72.4, 75.5, 75.4, 78.4, 75.1, 73.2, 76.9, 76.1, 73.3, 73.3, 74.7, 77.4, 76.6, 72.0, 76.5, 74.7, 71.8, 78.5, 72.9, 73.4, 71.9, 75.7, 75.6, 72.1, 72.2, 74.9, 72.1, 74.3, 74.8, 72.3, 75.1, 71.5, 75.5, 75.3, 73.1, 79.8, 74.8, 72.2, 74.8, 79.1, 77.2, 75.2, 77.3, 76.1, 73.7, 73.0, 76.7, 75.9, 72.2, 76.7, 77.3, 77.1, 74.3, 75.3, 74.7, 82.2, 76.1, 75.5, 76.0, 74.5, 75.6, 75.6, 76.8], "机动车-检出率": [58.9, 57.2, 56.7, 58.0, 56.6, 56.3, 52.4, 54.9, 56.0, 57.5, 56.4, 57.8, 57.6, 58.7, 56.6, 58.7, 56.5, 55.6, 56.7, 56.6, 54.0, 57.5, 56.8, 56.5, 56.7, 59.6, 56.1, 59.5, 57.8, 57.7, 55.4, 57.4, 57.6, 55.3, 55.6, 56.4, 58.9, 55.1, 58.6, 57.2, 58.1, 59.3, 59.5, 57.0, 56.3, 54.0, 56.0, 56.8, 58.2, 60.7, 56.3, 57.5, 55.4, 57.3, 56.8, 56.8, 58.5, 58.5, 57.4, 58.8, 57.2, 59.2, 60.3, 58.4, 57.7, 56.1, 54.0, 55.3, 56.9, 58.1, 57.2, 58.0, 57.6, 56.7, 57.1, 59.2, 57.1, 56.1, 53.9, 58.1, 53.2, 56.5, 54.9, 58.3, 57.0, 56.9, 56.3, 55.2, 55.4, 60.1, 56.6, 57.0, 56.1, 57.0, 55.9, 54.4, 57.5, 60.1, 57.8, 57.1], "行人-准确率": [86.6, 89.6, 89.8, 84.7, 87.8, 87.4, 88.8, 86.4, 88.5, 87.9, 85.7, 85.6, 87.5, 87.0, 86.2, 86.4, 86.2, 86.8, 87.3, 85.5, 88.0, 86.7, 86.7, 86.7, 86.0, 84.8, 90.0, 89.2, 86.9, 87.5, 88.7, 86.4, 86.1, 87.5, 87.1, 85.8, 85.8, 87.0, 88.3, 84.9, 85.9, 87.2, 87.8, 85.4, 87.2, 85.3, 87.6, 87.7, 88.2, 86.3, 86.0, 85.6, 84.6, 85.4, 87.1, 86.0, 85.1, 88.9, 88.7, 87.0, 87.2, 87.4, 86.5, 86.6, 87.7, 89.6, 86.3, 86.0, 84.6, 85.3, 88.1, 85.3, 86.5, 89.3, 86.4, 88.6, 85.9, 85.1, 86.7, 85.9, 86.2, 88.3, 89.6, 87.0, 85.7, 84.9, 89.6, 86.2, 85.9, 87.7, 85.9, 86.0, 88.8, 86.2, 84.6, 85.9, 85.4, 84.7, 85.6, 86.4], "行人-检出率": [67.7, 65.2, 67.5, 67.5, 67.1, 67.6, 63.1, 67.1, 65.1, 66.3, 67.0, 68.1, 67.9, 66.9, 68.8, 68.9, 67.6, 68.1, 66.2, 68.6, 66.4, 68.2, 67.5, 69.3, 67.5, 69.6, 65.2, 65.2, 67.1, 67.3, 68.2, 65.2, 70.7, 67.0, 66.4, 66.8, 68.0, 67.3, 66.7, 68.8, 68.6, 68.2, 67.9, 68.0, 67.9, 70.4, 66.5, 69.0, 67.1, 70.0, 66.2, 67.3, 68.8, 70.7, 64.7, 67.8, 66.0, 64.9, 68.5, 66.8, 67.9, 68.6, 66.5, 70.0, 69.9, 67.4, 68.7, 69.7, 68.3, 66.4, 70.0, 66.7, 66.3, 66.2, 69.5, 68.7, 68.0, 70.1, 67.5, 67.2, 67.0, 66.0, 68.5, 65.2, 67.2, 67.3, 68.3, 68.7, 67.3, 66.9, 67.1, 67.9, 67.0, 68.2, 70.6, 68.8, 66.7, 68.3, 69.7, 67.7], "非机动车-准确率": [71.3, 70.9, 70.4, 69.1, 67.2, 70.8, 65.2, 67.8, 69.1, 69.7, 70.4, 71.9, 71.0, 68.0, 70.2, 67.9, 72.0, 67.8, 71.2, 66.8, 69.5, 68.0, 68.7, 69.6, 67.4, 72.8, 70.8, 71.2, 67.9, 70.7, 69.8, 70.7, 66.9, 65.6, 73.8, 70.4, 68.7, 69.4, 70.7, 68.2, 68.8, 71.0, 72.3, 70.3, 66.8, 67.2, 71.6, 70.9, 72.3, 69.1, 70.3, 68.8, 70.0, 67.5, 66.6, 69.2, 72.2, 71.6, 68.1, 70.4, 71.2, 71.0, 70.5, 68.2, 68.4, 71.7, 70.4, 68.8, 68.9, 68.1, 71.6, 69.8, 70.1, 74.0, 66.9, 66.3, 69.2, 68.4, 67.3, 71.3, 70.6, 68.2, 72.0, 69.9, 68.1, 66.3, 68.7, 69.9, 69.0, 69.4, 69.0, 71.7, 71.3, 69.4, 69.1, 67.7, 71.4, 70.0, 69.5, 70.5], "非机动车-检出率": [51.0, 49.6, 51.1, 53.8, 55.8, 52.6, 52.7, 54.3, 50.2, 53.6, 53.4, 52.1, 54.0, 52.4, 51.9, 50.1, 55.1, 52.3, 49.5, 54.2, 54.0, 54.9, 53.3, 52.0, 52.5, 53.8, 52.1, 54.0, 51.4, 54.2, 55.1, 53.0, 53.2, 51.7, 51.3, 49.1, 50.7, 52.4, 50.4, 53.2, 51.7, 52.3, 50.9, 51.7, 51.9, 55.2, 55.2, 50.2, 52.2, 51.1, 55.3, 54.4, 52.8, 52.0, 51.9, 53.7, 52.5, 54.1, 53.6, 51.4, 52.8, 52.9, 53.4, 52.3, 52.5, 50.9, 51.8, 51.6, 52.7, 50.4, 51.6, 50.8, 50.8, 52.1, 51.7, 54.4, 53.1, 52.7, 53.0, 52.5, 54.6, 52.7, 50.4, 53.7, 52.0, 53.9, 51.4, 51.7, 51.9, 50.4, 51.5, 50.5, 54.7, 54.7, 49.8, 53.3, 51.2, 51.9, 52.0, 51.8]}, "总体": {"机动车-准确率": [82.6, 81.3, 82.0, 81.8, 79.8, 80.7, 80.9, 83.5, 80.7, 81.0, 79.6, 82.1, 81.5, 80.2, 80.5, 78.6, 83.9, 80.5, 80.6, 80.8, 83.8, 82.3, 82.9, 82.1, 80.9, 81.8, 81.5, 81.3, 82.4, 81.2, 79.0, 81.9, 82.6, 79.7, 80.7, 84.1, 81.2, 80.0, 81.2, 82.3, 84.7, 80.3, 79.1, 82.6, 81.4, 80.1, 80.4, 80.2, 81.0, 80.8, 79.5, 81.9, 80.6, 79.2, 83.5, 79.7, 79.1, 79.6, 80.3, 81.5, 80.1, 77.7, 81.6, 79.6, 80.7, 80.6, 80.4, 80.8, 77.9, 81.1, 79.6, 79.1, 83.3, 80.0, 79.2, 78.8, 83.4, 82.5, 79.9, 81.4, 81.8, 79.7, 80.8, 82.1, 81.6, 80.7, 81.3, 83.2, 82.0, 80.1, 80.9, 81.4, 84.2, 81.9, 80.8, 81.5, 80.0, 80.9, 81.4, 82.6], "机动车-检出率": [61.6, 63.4, 61.8, 63.1, 62.1, 62.5, 60.7, 62.5, 61.9, 63.8, 61.1, 61.4, 61.1, 63.8, 62.0, 62.9, 61.9, 60.5, 63.1, 62.5, 61.4, 62.8, 62.2, 61.5, 63.0, 64.1, 60.1, 63.1, 63.0, 62.3, 60.6, 62.3, 62.4, 61.5, 61.9, 62.1, 63.8, 61.1, 62.6, 61.8, 61.7, 63.1, 64.0, 61.6, 61.7, 62.2, 62.2, 62.8, 62.9, 63.8, 61.1, 63.3, 61.7, 62.0, 63.2, 62.6, 63.4, 62.5, 61.8, 62.5, 62.2, 62.9, 63.7, 63.3, 63.2, 61.2, 59.8, 61.1, 63.6, 62.9, 62.4, 62.4, 62.5, 61.6, 62.8, 63.4, 62.7, 61.8, 60.7, 63.1, 59.2, 61.8, 62.3, 61.9, 61.8, 61.8, 62.8, 59.8, 62.4, 62.7, 63.2, 62.3, 61.7, 63.0, 60.6, 62.4, 63.3, 63.5, 63.2, 62.3], "行人-准确率": [91.4, 93.0, 92.9, 90.5, 92.2, 92.2, 93.4, 91.1, 92.2, 92.0, 91.3, 91.2, 91.6, 92.0, 91.2, 91.0, 91.3, 91.7, 92.7, 90.9, 92.9, 92.0, 91.1, 91.3, 91.6, 90.0, 94.1, 93.2, 91.8, 92.4, 92.5, 92.4, 90.8, 92.4, 91.3, 91.0, 91.1, 91.7, 92.7, 90.6, 91.2, 91.8, 92.0, 91.3, 92.4, 91.1, 92.0, 91.7, 92.7, 90.6, 91.3, 90.9, 90.0, 91.0, 92.5, 91.8, 91.6, 92.7, 92.5, 91.6, 91.4, 92.1, 91.6, 91.8, 92.4, 93.1, 91.4, 91.8, 91.0, 90.5, 91.7, 90.8, 92.1, 92.4, 92.1, 92.4, 91.1, 90.5, 91.5, 91.1, 91.0, 92.3, 93.4, 92.1, 90.6, 90.2, 92.6, 91.6, 91.1, 92.2, 91.4, 91.8, 93.1, 91.3, 90.5, 90.7, 90.8, 89.9, 91.3, 91.6], "行人-检出率": [72.1, 72.1, 72.7, 72.2, 72.1, 72.2, 70.6, 71.2, 71.1, 72.8, 71.3, 71.9, 73.1, 71.0, 73.4, 73.8, 72.7, 72.4, 71.1, 72.7, 72.3, 73.5, 72.0, 73.8, 73.1, 73.4, 71.0, 72.4, 72.0, 71.8, 73.5, 71.9, 74.3, 71.1, 71.9, 72.3, 72.8, 72.7, 71.5, 72.7, 73.4, 73.4, 72.1, 73.3, 72.4, 73.7, 72.8, 73.3, 72.4, 74.0, 71.5, 72.1, 72.5, 74.1, 70.9, 73.1, 72.3, 71.0, 72.2, 72.1, 71.9, 73.0, 72.9, 73.3, 73.1, 73.7, 72.3, 74.0, 72.6, 71.6, 73.7, 72.6, 73.1, 72.0, 73.8, 72.5, 72.7, 73.5, 72.2, 72.0, 72.2, 71.0, 73.2, 71.3, 73.1, 72.7, 72.9, 73.2, 72.2, 72.3, 72.8, 71.8, 72.2, 73.0, 74.0, 73.0, 71.6, 72.1, 73.1, 72.0], "非机动车-准确率": [76.8, 77.1, 77.6, 75.7, 73.6, 76.4, 73.6, 74.8, 74.9, 75.8, 75.5, 76.8, 75.8, 75.1, 76.9, 74.4, 76.3, 73.9, 78.2, 73.5, 74.7, 75.0, 75.7, 77.4, 74.9, 76.8, 76.1, 77.0, 74.8, 75.9, 76.6, 75.8, 74.2, 73.9, 78.5, 74.3, 74.7, 76.2, 76.8, 74.9, 75.5, 76.5, 76.2, 76.3, 73.8, 75.0, 78.2, 75.0, 77.0, 75.5, 76.7, 75.6, 76.2, 73.5, 75.0, 74.8, 76.8, 76.3, 74.5, 76.0, 77.5, 76.7, 77.3, 74.5, 74.4, 76.7, 77.1, 75.9, 75.0, 75.9, 75.6, 75.4, 77.1, 78.1, 73.9, 75.1, 76.7, 74.9, 74.4, 75.6, 76.7, 74.7, 79.2, 74.9, 73.9, 72.6, 74.6, 76.3, 75.0, 75.8, 74.6, 77.2, 75.6, 74.7, 74.8, 74.3, 75.8, 76.4, 74.7, 76.7], "非机动车-检出率": [57.0, 56.8, 55.8, 58.2, 59.4, 57.8, 57.2, 57.8, 56.7, 57.5, 58.3, 56.0, 58.5, 56.9, 56.9, 56.1, 59.0, 58.0, 56.3, 59.1, 57.9, 58.4, 57.9, 56.6, 57.7, 57.3, 56.8, 58.6, 57.7, 58.1, 58.3, 57.1, 56.8, 56.7, 56.2, 56.9, 56.2, 57.0, 56.7, 58.5, 56.0, 56.1, 57.1, 56.7, 58.2, 58.7, 58.5, 57.5, 56.5, 56.3, 59.4, 57.9, 57.6, 56.3, 56.5, 58.5, 56.3, 57.8, 58.8, 57.1, 56.5, 57.1, 56.7, 58.0, 56.7, 57.3, 57.6, 56.1, 57.0, 55.4, 56.5, 56.9, 56.3, 56.4, 56.5, 58.0, 56.9, 58.3, 56.8, 57.4, 58.8, 56.6, 55.7, 58.7, 56.6, 58.3, 56.5, 57.0, 57.7, 56.3, 57.3, 56.2, 57.8, 57.1, 56.2, 58.0, 57.3, 57.9, 57.1, 57.0]}};

            // 颜色配置
            var masterCategoryColors = {
                '行人': ['#1f77b4', '#aec7e8'],
                '机动车': ['#2ca02c', '#98df8a'],
                '非机动车': ['#ff7f0e', '#ffbb78']
            };

            // 趋势图配置
            function getMasterTrendOption(distBin) {
                var seriesData = masterTrendData[distBin];
                var series = [];

                Object.keys(seriesData).forEach(function(seriesName) {
                    var category = seriesName.split('-')[0];
                    var metric = seriesName.split('-')[1];
                    var colorIndex = metric === '准确率' ? 0 : 1;
                    var lineType = metric === '准确率' ? 'solid' : 'dashed';

                    series.push({
                        name: seriesName,
                        type: 'line',
                        data: seriesData[seriesName],
                        smooth: false,
                        symbol: 'none',
                        lineStyle: {
                            color: masterCategoryColors[category][colorIndex],
                            width: 3,
                            type: lineType
                        },
                        emphasis: {
                            focus: 'series',
                            lineStyle: {
                                width: 4
                            }
                        }
                    });
                });

                return {
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    title: {
                        text: distBin + ' - 场景性能趋势',
                        left: 'center',
                        textStyle: {
                            color: '#5a6c7d',
                            fontSize: 16
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        borderColor: '#bdc3c7',
                        textStyle: {
                            color: '#2c3e50'
                        }
                    },
                    legend: {
                        top: '8%',
                        textStyle: {
                            color: '#5a6c7d'
                        }
                    },
                    grid: {
                        left: '10%',
                        right: '10%',
                        top: '20%',
                        bottom: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: masterScenes,
                        name: '场景',
                        axisLabel: {
                            color: '#7f8c8d',
                            rotate: 45
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#bdc3c7'
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '性能指标 (%)',
                        min: 0,
                        max: 100,
                        axisLabel: {
                            color: '#7f8c8d'
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#bdc3c7'
                            }
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#ecf0f1',
                                type: 'dashed'
                            }
                        }
                    },
                    series: series
                };
            }

            // 初始化趋势图
            var masterTrendChart = echarts.init(document.getElementById('master-trend-chart'));
            masterTrendChart.setOption(getMasterTrendOption('0-50米'));

            // 切换函数
            function switchMasterTrend(distBin, clickedButton) {
                // 更新按钮状态
                var buttons = document.querySelectorAll('#master-trend-buttons .master-trend-btn');
                buttons.forEach(btn => btn.classList.remove('master-trend-active'));
                clickedButton.classList.add('master-trend-active');

                // 更新趋势图
                masterTrendChart.setOption(getMasterTrendOption(distBin));
            }
        </script>

        <style>
            .master-trend-btn.master-trend-active {
                background: #00d4ff !important;
                color: #000000 !important;
            }
            .master-trend-btn:hover {
                background: #00d4ff !important;
                color: #000000 !important;
            }
        </style>
        
            </div>
        </body>
        </html>
        