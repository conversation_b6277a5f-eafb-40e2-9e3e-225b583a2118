# 数据分析可视化配置文件 - 商务科技风格

# 场景映射配置 - 注释掉以使用原始文件名
# scene_mapping:
#   "deqing-001": "德清场景001"
#   "deqing-002": "德清场景002"

# 距离分桶配置 (米)
distance_bins:
  - name: "总体"
    min: 0
    max: 150
  - name: "0-50米"
    min: 0
    max: 50
  - name: "50-100米"
    min: 50
    max: 100
  - name: "100-150米"
    min: 100
    max: 150

# 目标类别映射
category_mapping:
  "车": "机动车"
  "非机动车": "非机动车"
  "行人": "行人"

# 字段映射配置
field_mapping:
  scene_id: "场景ID"
  timestamp: "时间戳"
  distance_bin: "距离段"
  target_cls: "目标类别"
  tp: "真正例"
  fp: "假正例"
  fn: "假负例"
  recall: "检出率"
  precision: "准确率"
  f1: "F1分数"
  sample_cnt: "样本数"

# 商务科技风格颜色配置
color_palette:
  # 类别颜色 - 蓝绿橙配色方案
  person:
    primary: "#1f77b4"    # 蓝色
    secondary: "#aec7e8"  # 浅蓝色
  vehicle:
    primary: "#2ca02c"    # 绿色
    secondary: "#98df8a"  # 浅绿色
  non_vehicle:
    primary: "#ff7f0e"    # 橙色
    secondary: "#ffbb78"  # 浅橙色

  # 指标颜色
  recall: "#00d4ff"       # 科技蓝 - 检出率
  precision: "#00ff88"    # 科技绿 - 准确率
  f1: "#ff6b35"          # 科技橙 - F1分数

  # 表盘颜色阈值
  gauge_good: "#00ff88"   # 绿色 >80%
  gauge_medium: "#ffaa00" # 黄色 60%-80%
  gauge_poor: "#ff4444"   # 红色 <60%

# 深色科技主题配置
chart_theme:
  background_color: "#1e1e1e"      # 深色背景
  secondary_bg: "#2d2d2d"          # 次级背景
  grid_color: "#404040"            # 网格线颜色
  text_color: "#ffffff"            # 主文字颜色
  secondary_text: "#cccccc"        # 次级文字颜色
  accent_color: "#00d4ff"          # 强调色
  font_family: "Microsoft YaHei, 'Segoe UI', Arial, sans-serif"
  font_size: 12

# 报告配置
report:
  title: "多场景检测性能分析报告"
  subtitle: "商务科技风格可视化报告"
  author: "AI助手"
  output_dir: "report"
  template: "dark_tech"
