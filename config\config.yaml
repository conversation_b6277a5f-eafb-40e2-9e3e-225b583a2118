# 数据分析可视化配置文件 - 商务科技风格

# 场景映射配置 - 注释掉以使用原始文件名
# scene_mapping:
#   "deqing-001": "德清场景001"
#   "deqing-002": "德清场景002"

# 距离分桶配置 (米)
distance_bins:
  - name: "总体"
    min: 0
    max: 150
  - name: "0-50米"
    min: 0
    max: 50
  - name: "50-100米"
    min: 50
    max: 100
  - name: "100-150米"
    min: 100
    max: 150

# 目标类别映射
category_mapping:
  "车": "机动车"
  "非机动车": "非机动车"
  "行人": "行人"

# 字段映射配置
field_mapping:
  scene_id: "场景ID"
  timestamp: "时间戳"
  distance_bin: "距离段"
  target_cls: "目标类别"
  tp: "真正例"
  fp: "假正例"
  fn: "假负例"
  recall: "检出率"
  precision: "准确率"
  f1: "F1分数"
  sample_cnt: "样本数"

# 浅色科技风格颜色配置
color_palette:
  # 类别颜色 - 现代渐变配色方案
  person:
    primary: "#3b82f6"    # 现代蓝
    secondary: "#93c5fd"  # 浅蓝
  vehicle:
    primary: "#10b981"    # 现代绿
    secondary: "#6ee7b7"  # 浅绿
  non_vehicle:
    primary: "#f59e0b"    # 现代橙
    secondary: "#fbbf24"  # 浅橙

  # 指标颜色
  recall: "#3b82f6"       # 现代蓝 - 检出率
  precision: "#10b981"    # 现代绿 - 准确率
  f1: "#8b5cf6"          # 现代紫 - F1分数

  # 表盘颜色阈值
  gauge_good: "#10b981"   # 现代绿 >80%
  gauge_medium: "#f59e0b" # 现代橙 60%-80%
  gauge_poor: "#ef4444"   # 现代红 <60%

# 浅色科技主题配置
chart_theme:
  background_color: "#f8fafc"      # 浅色背景
  secondary_bg: "#ffffff"          # 次级背景（纯白）
  grid_color: "#e2e8f0"            # 网格线颜色（浅灰）
  text_color: "#1e293b"            # 主文字颜色（深灰）
  secondary_text: "#64748b"        # 次级文字颜色（中灰）
  accent_color: "#3b82f6"          # 强调色（现代蓝）
  font_family: "Microsoft YaHei, 'Segoe UI', Arial, sans-serif"
  font_size: 12

# 报告配置
report:
  title: "多场景检测性能分析报告"
  subtitle: "浅色科技风格可视化报告"
  author: "AI助手"
  output_dir: "report"
  template: "dark_tech"
