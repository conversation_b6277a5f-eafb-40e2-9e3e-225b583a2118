
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>deqing-032 - 性能分析报告</title>
                <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
                <style>
                    body {
                        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                        color: #1e293b;
                        font-family: Microsoft YaHei, 'Segoe UI', Arial, sans-serif;
                        margin: 0;
                        padding: 20px;
                        min-height: 100vh;
                    }
                    .section {
                        margin: 30px 0;
                        padding: 25px;
                        background: #ffffff;
                        border-radius: 16px;
                        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.1);
                        border: 1px solid #e2e8f0;
                        backdrop-filter: blur(10px);
                        transition: transform 0.2s ease, box-shadow 0.2s ease;
                    }
                    .section:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08);
                    }
                    .gauge-container {
                        display: flex;
                        justify-content: space-around;
                        align-items: center;
                        flex-wrap: wrap;
                    }
                    h1 {
                        text-align: center;
                        color: #1e293b;
                        font-weight: 600;
                        font-size: 2.5rem;
                        margin-bottom: 10px;
                        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        background-clip: text;
                    }
                    h2 {
                        text-align: center;
                        color: #64748b;
                        font-weight: 500;
                        font-size: 1.8rem;
                        margin-bottom: 20px;
                    }
                </style>
            </head>
            <body>
                <h1>deqing-032 - 性能分析报告</h1>

                <div class="section">
                    <h2>总体性能指标</h2>
                    
        <div id="gauge-container-deqing-032" style="width: 100%; height: 400px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: #2c3e50; margin-bottom: 15px;">
                    deqing-032 - 总体性能仪表盘
                </h3>
            </div>
            <div id="gauge-chart-deqing-032" style="width: 100%; height: 350px;"></div>
        </div>

        <script>
            // 初始化仪表盘
            var gaugeChart_deqing_032 = echarts.init(document.getElementById('gauge-chart-deqing-032'));

            var gaugeOption_deqing_032 = {
                backgroundColor: 'rgba(255, 255, 255, 0.5)',
                series: [
                    
            {
                name: '检出率',
                type: 'gauge',
                center: ['25%', '50%'],
                radius: '70%',
                min: 0,
                max: 100,
                splitNumber: 10,
                axisLine: {
                    lineStyle: {
                        color: [[0.3, '#e67e22'], [0.7, '#3498db'], [1, '#3498db']],
                        width: 8
                    }
                },
                pointer: {
                    itemStyle: { color: 'auto' }
                },
                axisTick: {
                    distance: -30,
                    length: 8,
                    lineStyle: { color: '#fff', width: 2 }
                },
                splitLine: {
                    distance: -30,
                    length: 30,
                    lineStyle: { color: '#fff', width: 4 }
                },
                axisLabel: {
                    color: 'auto',
                    distance: 40,
                    fontSize: 12
                },
                detail: {
                    valueAnimation: true,
                    formatter: '{value}%',
                    color: '#3498db',
                    fontSize: 16,
                    offsetCenter: [0, '70%']
                },
                title: {
                    offsetCenter: [0, '90%'],
                    fontSize: 14,
                    color: '#5a6c7d'
                },
                data: [{ value: 63.7, name: '检出率' }]
            }
            ,
                    
            {
                name: '准确率',
                type: 'gauge',
                center: ['75%', '50%'],
                radius: '70%',
                min: 0,
                max: 100,
                splitNumber: 10,
                axisLine: {
                    lineStyle: {
                        color: [[0.3, '#e67e22'], [0.7, '#f39c12'], [1, '#2ecc71']],
                        width: 8
                    }
                },
                pointer: {
                    itemStyle: { color: 'auto' }
                },
                axisTick: {
                    distance: -30,
                    length: 8,
                    lineStyle: { color: '#fff', width: 2 }
                },
                splitLine: {
                    distance: -30,
                    length: 30,
                    lineStyle: { color: '#fff', width: 4 }
                },
                axisLabel: {
                    color: 'auto',
                    distance: 40,
                    fontSize: 12
                },
                detail: {
                    valueAnimation: true,
                    formatter: '{value}%',
                    color: '#2ecc71',
                    fontSize: 16,
                    offsetCenter: [0, '70%']
                },
                title: {
                    offsetCenter: [0, '90%'],
                    fontSize: 14,
                    color: '#5a6c7d'
                },
                data: [{ value: 83.4, name: '准确率' }]
            }
            
                ]
            };

            gaugeChart_deqing_032.setOption(gaugeOption_deqing_032);
            window.addEventListener('resize', () => gaugeChart_deqing_032.resize());
        </script>
        
                </div>

                <div class="section">
                    <h2>距离段性能对比</h2>
                    
        <div id="bar-chart-deqing_032" style="width: 1000px; height: 500px; margin: 20px auto;"></div>

        <script>
            // 初始化柱状图
            var barChart_deqing_032 = echarts.init(document.getElementById('bar-chart-deqing_032'));

            var barOption_deqing_032 = {
  "backgroundColor": "#f8fafc",
  "title": {
    "text": "deqing-032 - 距离段性能对比",
    "subtext": "不同目标类别在各距离段的准确率和检出率对比",
    "textStyle": {
      "color": "#1e293b",
      "fontSize": 16
    },
    "subtextStyle": {
      "color": "#64748b",
      "fontSize": 12
    }
  },
  "tooltip": {
    "trigger": "axis",
    "backgroundColor": "rgba(45, 45, 45, 0.95)",
    "borderColor": "#e2e8f0",
    "textStyle": {
      "color": "#ffffff"
    }
  },
  "legend": {
    "top": "8%",
    "textStyle": {
      "color": "#1e293b"
    }
  },
  "xAxis": {
    "type": "category",
    "data": [
      "0-50米",
      "50-100米",
      "总体"
    ],
    "name": "距离段",
    "axisLabel": {
      "color": "#1e293b"
    },
    "axisLine": {
      "lineStyle": {
        "color": "#e2e8f0"
      }
    }
  },
  "yAxis": {
    "type": "value",
    "name": "性能指标 (%)",
    "min": 0,
    "max": 100,
    "axisLabel": {
      "color": "#1e293b"
    },
    "axisLine": {
      "lineStyle": {
        "color": "#e2e8f0"
      }
    },
    "splitLine": {
      "show": true,
      "lineStyle": {
        "color": "#e2e8f0",
        "type": "dashed"
      }
    }
  },
  "series": [
    {
      "name": "机动车-准确率",
      "type": "bar",
      "data": [
        87.6,
        76.0,
        81.9
      ],
      "itemStyle": {
        "color": "#10b981"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "机动车-检出率",
      "type": "bar",
      "data": [
        67.1,
        57.4,
        62.3
      ],
      "itemStyle": {
        "color": "#6ee7b7"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "行人-准确率",
      "type": "bar",
      "data": [
        97.4,
        86.4,
        92.4
      ],
      "itemStyle": {
        "color": "#3b82f6"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "行人-检出率",
      "type": "bar",
      "data": [
        77.8,
        65.2,
        71.9
      ],
      "itemStyle": {
        "color": "#93c5fd"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "非机动车-准确率",
      "type": "bar",
      "data": [
        80.8,
        70.7,
        75.8
      ],
      "itemStyle": {
        "color": "#f59e0b"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "非机动车-检出率",
      "type": "bar",
      "data": [
        61.2,
        53.0,
        57.1
      ],
      "itemStyle": {
        "color": "#fbbf24"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    }
  ]
};

            barChart_deqing_032.setOption(barOption_deqing_032);
            window.addEventListener('resize', () => barChart_deqing_032.resize());
        </script>
        
                </div>

                <div class="section">
                    <h2>雷达图分析</h2>
                    
        <style>
            .radar-btn:hover {
                background: #3b82f6 !important;
                color: white !important;
                border-color: #3b82f6 !important;
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(59,130,246,0.3) !important;
            }
            .radar-active {
                background: #3b82f6 !important;
                color: white !important;
                border-color: #3b82f6 !important;
                box-shadow: 0 4px 12px rgba(59,130,246,0.3) !important;
            }
        </style>
        <div id="radar-container-deqing-032" style="width: 100%; height: 600px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: #1e293b; margin-bottom: 15px;">
                    deqing-032 - 雷达图分析
                </h3>
                <div id="radar-buttons-deqing-032" style="margin-bottom: 20px;">
        
                    <button onclick="switchRadar_deqing_032('0-50米', this)"
                            class="radar-btn radar-active"
                            style="margin: 0 10px; padding: 10px 20px;
                                   background: #ffffff;
                                   color: #1e293b;
                                   border: 2px solid #e2e8f0;
                                   border-radius: 8px; cursor: pointer;
                                   font-weight: 500; transition: all 0.2s ease;
                                   box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        0-50米
                    </button>
            
                    <button onclick="switchRadar_deqing_032('50-100米', this)"
                            class="radar-btn "
                            style="margin: 0 10px; padding: 10px 20px;
                                   background: #ffffff;
                                   color: #1e293b;
                                   border: 2px solid #e2e8f0;
                                   border-radius: 8px; cursor: pointer;
                                   font-weight: 500; transition: all 0.2s ease;
                                   box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        50-100米
                    </button>
            
                    <button onclick="switchRadar_deqing_032('总体', this)"
                            class="radar-btn "
                            style="margin: 0 10px; padding: 10px 20px;
                                   background: #ffffff;
                                   color: #1e293b;
                                   border: 2px solid #e2e8f0;
                                   border-radius: 8px; cursor: pointer;
                                   font-weight: 500; transition: all 0.2s ease;
                                   box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        总体
                    </button>
            
                </div>
            </div>
            <div id="radar-chart-deqing-032" style="width: 100%; height: 500px;"></div>
        </div>

        <script>
            // 雷达图数据
            var radarData_deqing_032 = {"0-50米": [87.60806916426513, 67.10816777041943, 97.39884393063583, 77.82909930715935, 80.76923076923077, 61.165048543689316], "50-100米": [75.97597597597597, 57.36961451247166, 86.36363636363636, 65.17150395778364, 70.7395498392283, 53.01204819277109], "总体": [81.91176470588235, 62.304250559284114, 92.40506329113924, 71.92118226600985, 75.76243980738363, 57.073760580411125]};

            // 雷达图配置
            var radarOption_deqing_032 = {
                backgroundColor: '#f8fafc',
                radar: {
                    indicator: [
                        {name: '人-准确率', max: 100},
                        {name: '人-检出率', max: 100},
                        {name: '车-准确率', max: 100},
                        {name: '车-检出率', max: 100},
                        {name: '非-准确率', max: 100},
                        {name: '非-检出率', max: 100}
                    ],
                    center: ['50%', '50%'],
                    radius: '70%',
                    axisName: {
                        color: '#1e293b',
                        fontSize: 12
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#e2e8f0'
                        }
                    },
                    splitArea: {
                        show: false
                    }
                },
                series: [{
                    type: 'radar',
                    data: [{
                        value: radarData_deqing_032['0-50米'],
                        name: '0-50米',
                        areaStyle: {
                            color: 'rgba(0, 212, 255, 0.3)'
                        },
                        lineStyle: {
                            color: '#3b82f6'
                        }
                    }]
                }]
            };

            // 初始化雷达图
            var radarChart_deqing_032 = echarts.init(document.getElementById('radar-chart-deqing-032'));
            radarChart_deqing_032.setOption(radarOption_deqing_032);

            // 切换函数
            function switchRadar_deqing_032(distBin, clickedButton) {
                // 更新按钮状态
                var buttons = document.querySelectorAll('#radar-buttons-deqing-032 .radar-btn');
                buttons.forEach(btn => btn.classList.remove('radar-active'));
                clickedButton.classList.add('radar-active');

                // 更新雷达图数据
                radarOption_deqing_032.series[0].data[0].value = radarData_deqing_032[distBin];
                radarOption_deqing_032.series[0].data[0].name = distBin;
                radarChart_deqing_032.setOption(radarOption_deqing_032);
            }
        </script>

        <style>
            .radar-btn.radar-active {
                background: #3b82f6 !important;
                color: #000000 !important;
            }
            .radar-btn:hover {
                background: #3b82f6 !important;
                color: #000000 !important;
            }
        </style>
        
                </div>

                <div class="section">
                    <h2>时间趋势分析</h2>
                    
        <style>
            .trend-btn:hover {
                background: #3b82f6 !important;
                color: white !important;
                border-color: #3b82f6 !important;
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(59,130,246,0.3) !important;
            }
            .trend-active {
                background: #3b82f6 !important;
                color: white !important;
                border-color: #3b82f6 !important;
                box-shadow: 0 4px 12px rgba(59,130,246,0.3) !important;
            }
        </style>
        <div id="trend-container-deqing-032" style="width: 100%; height: 700px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: #1e293b; margin-bottom: 15px;">
                    deqing-032 - 时间趋势分析
                </h3>
                <div id="trend-buttons-deqing-032" style="margin-bottom: 20px;">
        
                    <button onclick="switchTrend_deqing_032('0-50米', this)"
                            class="trend-btn trend-active"
                            style="margin: 0 10px; padding: 10px 20px;
                                   background: #ffffff;
                                   color: #1e293b;
                                   border: 2px solid #e2e8f0;
                                   border-radius: 8px; cursor: pointer;
                                   font-weight: 500; transition: all 0.2s ease;
                                   box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        0-50米
                    </button>
            
                    <button onclick="switchTrend_deqing_032('50-100米', this)"
                            class="trend-btn "
                            style="margin: 0 10px; padding: 10px 20px;
                                   background: #ffffff;
                                   color: #1e293b;
                                   border: 2px solid #e2e8f0;
                                   border-radius: 8px; cursor: pointer;
                                   font-weight: 500; transition: all 0.2s ease;
                                   box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        50-100米
                    </button>
            
                    <button onclick="switchTrend_deqing_032('总体', this)"
                            class="trend-btn "
                            style="margin: 0 10px; padding: 10px 20px;
                                   background: #ffffff;
                                   color: #1e293b;
                                   border: 2px solid #e2e8f0;
                                   border-radius: 8px; cursor: pointer;
                                   font-weight: 500; transition: all 0.2s ease;
                                   box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        总体
                    </button>
            
                </div>
            </div>
            <div id="trend-chart-deqing-032" style="width: 100%; height: 600px;"></div>
        </div>

        <script>
            // 时间戳数据
            var timestamps_deqing_032 = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66];

            // 趋势图数据
            var trendData_deqing_032 = {"0-50米": {"机动车-准确率": [71.4, 83.3, 100.0, 100.0, 87.5, 80.0, 100.0, 100.0, 100.0, 100.0, 100.0, 80.0, 77.8, 100.0, 100.0, 100.0, 75.0, 100.0, 75.0, 88.9, 100.0, 100.0, 100.0, 100.0, 100.0, 70.0, 77.8, 100.0, 87.5, 100.0, 100.0, 85.7, 83.3, 100.0, 75.0, 83.3, 85.7, 100.0, 80.0, 100.0, 100.0, 100.0, 80.0, 80.0, 83.3, 80.0, 80.0, 85.7, 100.0, 100.0, 80.0, 83.3, 85.7, 83.3, 75.0, 100.0, 100.0, 100.0, 87.5, 77.8, 100.0, 100.0, 100.0, 87.5, 70.0], "机动车-检出率": [71.4, 55.6, 62.5, 33.3, 70.0, 80.0, 60.0, 60.0, 66.7, 80.0, 50.0, 66.7, 70.0, 50.0, 75.0, 33.3, 75.0, 50.0, 75.0, 80.0, 77.8, 60.0, 66.7, 66.7, 80.0, 77.8, 70.0, 50.0, 77.8, 66.7, 80.0, 66.7, 83.3, 66.7, 50.0, 71.4, 66.7, 75.0, 50.0, 75.0, 62.5, 75.0, 50.0, 66.7, 62.5, 80.0, 80.0, 60.0, 60.0, 60.0, 66.7, 71.4, 60.0, 62.5, 60.0, 62.5, 50.0, 60.0, 77.8, 77.8, 50.0, 57.1, 57.1, 77.8, 77.8], "行人-准确率": [100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 88.9, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 85.7, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 88.9, 100.0, 100.0, 100.0, 88.9, 100.0, 85.7, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 83.3, 100.0, 100.0, 87.5, 100.0, 100.0, 87.5, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 87.5], "行人-检出率": [80.0, 88.9, 71.4, 70.0, 75.0, 66.7, 66.7, 80.0, 88.9, 66.7, 80.0, 66.7, 66.7, 75.0, 71.4, 75.0, 90.0, 85.7, 83.3, 66.7, 66.7, 80.0, 83.3, 66.7, 80.0, 80.0, 83.3, 88.9, 75.0, 87.5, 83.3, 80.0, 80.0, 75.0, 83.3, 85.7, 80.0, 87.5, 75.0, 75.0, 80.0, 75.0, 77.8, 66.7, 71.4, 88.9, 71.4, 77.8, 66.7, 70.0, 77.8, 66.7, 80.0, 80.0, 75.0, 83.3, 75.0, 75.0, 83.3, 75.0, 66.7, 87.5, 75.0, 66.7, 77.8], "非机动车-准确率": [100.0, 71.4, 75.0, 80.0, 80.0, 75.0, 83.3, 70.0, 85.7, 100.0, 83.3, 100.0, 66.7, 100.0, 100.0, 66.7, 83.3, 66.7, 75.0, 100.0, 66.7, 100.0, 100.0, 100.0, 100.0, 75.0, 75.0, 100.0, 100.0, 80.0, 66.7, 100.0, 100.0, 70.0, 66.7, 100.0, 75.0, 80.0, 100.0, 75.0, 100.0, 62.5, 80.0, 77.8, 100.0, 100.0, 100.0, 66.7, 100.0, 100.0, 80.0, 100.0, 70.0, 100.0, 100.0, 100.0, 71.4, 87.5, 62.5, 100.0, 100.0, 62.5, 75.0, 100.0, 83.3], "非机动车-检出率": [33.3, 71.4, 50.0, 50.0, 80.0, 75.0, 71.4, 70.0, 60.0, 50.0, 50.0, 40.0, 80.0, 57.1, 66.7, 66.7, 71.4, 50.0, 60.0, 33.3, 66.7, 33.3, 60.0, 57.1, 80.0, 75.0, 75.0, 42.9, 66.7, 57.1, 50.0, 33.3, 60.0, 70.0, 66.7, 50.0, 50.0, 66.7, 60.0, 60.0, 50.0, 71.4, 57.1, 70.0, 60.0, 33.3, 33.3, 66.7, 55.6, 42.9, 57.1, 60.0, 70.0, 66.7, 66.7, 50.0, 50.0, 77.8, 71.4, 50.0, 66.7, 62.5, 75.0, 66.7, 71.4]}, "50-100米": {"机动车-准确率": [100.0, 83.3, 100.0, 60.0, 83.3, 57.1, 71.4, 62.5, 100.0, 80.0, 75.0, 100.0, 75.0, 75.0, 62.5, 100.0, 70.0, 100.0, 55.6, 75.0, 100.0, 80.0, 87.5, 57.1, 100.0, 80.0, 66.7, 80.0, 71.4, 71.4, 100.0, 100.0, 83.3, 58.3, 60.0, 80.0, 100.0, 80.0, 60.0, 100.0, 60.0, 100.0, 54.5, 100.0, 87.5, 100.0, 100.0, 100.0, 60.0, 100.0, 60.0, 100.0, 100.0, 57.1, 66.7, 100.0, 66.7, 100.0, 83.3, 60.0, 100.0, 87.5, 66.7, 85.7, 83.3], "机动车-检出率": [33.3, 50.0, 25.0, 42.9, 62.5, 44.4, 62.5, 71.4, 37.5, 50.0, 50.0, 50.0, 66.7, 42.9, 71.4, 50.0, 70.0, 50.0, 50.0, 75.0, 80.0, 66.7, 70.0, 80.0, 60.0, 44.4, 33.3, 40.0, 62.5, 71.4, 66.7, 50.0, 71.4, 70.0, 75.0, 40.0, 33.3, 66.7, 60.0, 40.0, 42.9, 25.0, 66.7, 60.0, 77.8, 60.0, 50.0, 40.0, 50.0, 33.3, 42.9, 33.3, 66.7, 57.1, 50.0, 33.3, 50.0, 60.0, 71.4, 60.0, 50.0, 70.0, 75.0, 75.0, 71.4], "行人-准确率": [100.0, 100.0, 83.3, 100.0, 100.0, 80.0, 71.4, 100.0, 100.0, 100.0, 71.4, 71.4, 100.0, 71.4, 75.0, 100.0, 100.0, 75.0, 100.0, 100.0, 100.0, 75.0, 71.4, 100.0, 85.7, 75.0, 71.4, 80.0, 100.0, 100.0, 75.0, 85.7, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 66.7, 75.0, 66.7, 100.0, 100.0, 100.0, 100.0, 100.0, 85.7, 100.0, 71.4, 100.0, 100.0, 100.0, 100.0, 100.0, 87.5, 75.0, 75.0, 100.0, 100.0, 100.0, 100.0], "行人-检出率": [66.7, 66.7, 55.6, 50.0, 66.7, 80.0, 62.5, 50.0, 75.0, 60.0, 62.5, 62.5, 62.5, 71.4, 75.0, 80.0, 60.0, 75.0, 71.4, 50.0, 50.0, 75.0, 62.5, 71.4, 60.0, 75.0, 55.6, 66.7, 66.7, 66.7, 75.0, 75.0, 75.0, 83.3, 66.7, 60.0, 50.0, 57.1, 66.7, 80.0, 50.0, 60.0, 66.7, 75.0, 80.0, 33.3, 62.5, 57.1, 75.0, 80.0, 66.7, 50.0, 55.6, 66.7, 60.0, 66.7, 60.0, 60.0, 70.0, 50.0, 60.0, 75.0, 33.3, 60.0, 80.0], "非机动车-准确率": [50.0, 71.4, 80.0, 75.0, 100.0, 66.7, 100.0, 100.0, 100.0, 80.0, 60.0, 66.7, 100.0, 62.5, 100.0, 100.0, 66.7, 85.7, 100.0, 85.7, 50.0, 87.5, 62.5, 66.7, 83.3, 100.0, 60.0, 100.0, 71.4, 66.7, 66.7, 46.2, 100.0, 80.0, 66.7, 71.4, 66.7, 60.0, 50.0, 66.7, 66.7, 50.0, 57.1, 100.0, 50.0, 75.0, 83.3, 66.7, 100.0, 100.0, 100.0, 75.0, 50.0, 66.7, 62.5, 100.0, 100.0, 80.0, 77.8, 58.3, 75.0, 100.0, 100.0, 57.1, 100.0], "非机动车-检出率": [50.0, 62.5, 44.4, 60.0, 60.0, 40.0, 60.0, 50.0, 50.0, 66.7, 60.0, 40.0, 33.3, 62.5, 40.0, 66.7, 66.7, 75.0, 66.7, 66.7, 37.5, 77.8, 71.4, 33.3, 55.6, 33.3, 60.0, 50.0, 55.6, 40.0, 50.0, 66.7, 50.0, 66.7, 57.1, 55.6, 40.0, 33.3, 50.0, 50.0, 40.0, 75.0, 66.7, 25.0, 50.0, 37.5, 62.5, 50.0, 25.0, 33.3, 37.5, 37.5, 57.1, 50.0, 71.4, 25.0, 66.7, 57.1, 77.8, 70.0, 42.9, 40.0, 33.3, 50.0, 33.3]}, "总体": {"机动车-准确率": [85.7, 83.3, 100.0, 80.0, 85.4, 68.6, 85.7, 81.2, 100.0, 90.0, 87.5, 90.0, 76.4, 87.5, 81.2, 100.0, 72.5, 100.0, 65.3, 81.9, 100.0, 90.0, 93.8, 78.6, 100.0, 75.0, 72.2, 90.0, 79.5, 85.7, 100.0, 92.9, 83.3, 79.2, 67.5, 81.7, 92.9, 90.0, 70.0, 100.0, 80.0, 100.0, 67.3, 90.0, 85.4, 90.0, 90.0, 92.9, 80.0, 100.0, 70.0, 91.7, 92.9, 70.2, 70.8, 100.0, 83.3, 100.0, 85.4, 68.9, 100.0, 93.8, 83.3, 86.6, 76.7], "机动车-检出率": [52.4, 52.8, 43.8, 38.1, 66.2, 62.2, 61.3, 65.7, 52.1, 65.0, 50.0, 58.3, 68.3, 46.4, 73.2, 41.7, 72.5, 50.0, 62.5, 77.5, 78.9, 63.3, 68.3, 73.3, 70.0, 61.1, 51.7, 45.0, 70.1, 69.0, 73.3, 58.3, 77.4, 68.3, 62.5, 55.7, 50.0, 70.8, 55.0, 57.5, 52.7, 50.0, 58.3, 63.3, 70.1, 70.0, 65.0, 50.0, 55.0, 46.7, 54.8, 52.4, 63.3, 59.8, 55.0, 47.9, 50.0, 60.0, 74.6, 68.9, 50.0, 63.6, 66.1, 76.4, 74.6], "行人-准确率": [100.0, 100.0, 91.7, 100.0, 100.0, 90.0, 85.7, 100.0, 94.4, 100.0, 85.7, 85.7, 100.0, 85.7, 87.5, 100.0, 100.0, 80.4, 100.0, 100.0, 100.0, 87.5, 85.7, 100.0, 92.9, 87.5, 85.7, 84.4, 100.0, 100.0, 87.5, 87.3, 100.0, 92.9, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 83.3, 87.5, 75.0, 100.0, 100.0, 93.8, 100.0, 100.0, 86.6, 100.0, 85.7, 100.0, 100.0, 100.0, 100.0, 100.0, 93.8, 87.5, 87.5, 100.0, 100.0, 100.0, 93.8], "行人-检出率": [73.3, 77.8, 63.5, 60.0, 70.8, 73.3, 64.6, 65.0, 81.9, 63.3, 71.2, 64.6, 64.6, 73.2, 73.2, 77.5, 75.0, 80.4, 77.4, 58.3, 58.3, 77.5, 72.9, 69.0, 70.0, 77.5, 69.4, 77.8, 70.8, 77.1, 79.2, 77.5, 77.5, 79.2, 75.0, 72.9, 65.0, 72.3, 70.8, 77.5, 65.0, 67.5, 72.2, 70.8, 75.7, 61.1, 67.0, 67.5, 70.8, 75.0, 72.2, 58.3, 67.8, 73.3, 67.5, 75.0, 67.5, 67.5, 76.7, 62.5, 63.3, 81.2, 54.2, 63.3, 78.9], "非机动车-准确率": [75.0, 71.4, 77.5, 77.5, 90.0, 70.8, 91.7, 85.0, 92.9, 90.0, 71.7, 83.3, 83.3, 81.2, 100.0, 83.3, 75.0, 76.2, 87.5, 92.9, 58.3, 93.8, 81.2, 83.3, 91.7, 87.5, 67.5, 100.0, 85.7, 73.3, 66.7, 73.1, 100.0, 75.0, 66.7, 85.7, 70.8, 70.0, 75.0, 70.8, 83.3, 56.2, 68.6, 88.9, 75.0, 87.5, 91.7, 66.7, 100.0, 100.0, 90.0, 87.5, 60.0, 83.3, 81.2, 100.0, 85.7, 83.8, 70.1, 79.2, 87.5, 81.2, 87.5, 78.6, 91.7], "非机动车-检出率": [41.7, 67.0, 47.2, 55.0, 70.0, 57.5, 65.7, 60.0, 55.0, 58.3, 55.0, 40.0, 56.7, 59.8, 53.3, 66.7, 69.0, 62.5, 63.3, 50.0, 52.1, 55.6, 65.7, 45.2, 67.8, 54.2, 67.5, 46.4, 61.1, 48.6, 50.0, 50.0, 55.0, 68.3, 61.9, 52.8, 45.0, 50.0, 55.0, 55.0, 45.0, 73.2, 61.9, 47.5, 55.0, 35.4, 47.9, 58.3, 40.3, 38.1, 47.3, 48.8, 63.6, 58.3, 69.0, 37.5, 58.3, 67.5, 74.6, 60.0, 54.8, 51.2, 54.2, 58.3, 52.4]}};

            // 颜色配置
            var categoryColors_deqing_032 = {
                '行人': ['#3b82f6', '#93c5fd'],
                '机动车': ['#10b981', '#6ee7b7'],
                '非机动车': ['#f59e0b', '#fbbf24']
            };

            // 趋势图配置
            function getTrendOption_deqing_032(distBin) {
                var seriesData = trendData_deqing_032[distBin];
                var series = [];

                Object.keys(seriesData).forEach(function(seriesName) {
                    var category = seriesName.split('-')[0];
                    var metric = seriesName.split('-')[1];
                    var colorIndex = metric === '准确率' ? 0 : 1;
                    var lineType = metric === '准确率' ? 'solid' : 'dashed';

                    series.push({
                        name: seriesName,
                        type: 'line',
                        data: seriesData[seriesName],
                        smooth: false,
                        symbol: 'none',
                        lineStyle: {
                            color: categoryColors_deqing_032[category][colorIndex],
                            width: 3,
                            type: lineType
                        },
                        emphasis: {
                            focus: 'series',
                            lineStyle: {
                                width: 4
                            }
                        }
                    });
                });

                return {
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    title: {
                        text: distBin + ' - 性能趋势',
                        left: 'center',
                        textStyle: {
                            color: '#5a6c7d',
                            fontSize: 16
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        borderColor: '#bdc3c7',
                        textStyle: {
                            color: '#2c3e50'
                        }
                    },
                    legend: {
                        top: '8%',
                        textStyle: {
                            color: '#5a6c7d'
                        }
                    },
                    grid: {
                        left: '10%',
                        right: '10%',
                        top: '20%',
                        bottom: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: timestamps_deqing_032,
                        name: '帧号',
                        axisLabel: {
                            color: '#7f8c8d'
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#bdc3c7'
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '性能指标 (%)',
                        min: 0,
                        max: 100,
                        axisLabel: {
                            color: '#7f8c8d'
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#bdc3c7'
                            }
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#ecf0f1',
                                type: 'dashed'
                            }
                        }
                    },
                    series: series
                };
            }

            // 初始化趋势图
            var trendChart_deqing_032 = echarts.init(document.getElementById('trend-chart-deqing-032'));
            trendChart_deqing_032.setOption(getTrendOption_deqing_032('0-50米'));

            // 切换函数
            function switchTrend_deqing_032(distBin, clickedButton) {
                // 更新按钮状态
                var buttons = document.querySelectorAll('#trend-buttons-deqing-032 .trend-btn');
                buttons.forEach(btn => btn.classList.remove('trend-active'));
                clickedButton.classList.add('trend-active');

                // 更新趋势图
                trendChart_deqing_032.setOption(getTrendOption_deqing_032(distBin));
            }
        </script>

        <style>
            .trend-btn.trend-active {
                background: #3b82f6 !important;
                color: #000000 !important;
            }
            .trend-btn:hover {
                background: #3b82f6 !important;
                color: #000000 !important;
            }
        </style>
        
                </div>
            </body>
            </html>
            