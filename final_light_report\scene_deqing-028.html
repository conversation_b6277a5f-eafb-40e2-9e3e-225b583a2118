
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>deqing-028 - 性能分析报告</title>
                <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
                <style>
                    body {
                        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                        color: #1e293b;
                        font-family: Microsoft YaHei, 'Segoe UI', Arial, sans-serif;
                        margin: 0;
                        padding: 20px;
                        min-height: 100vh;
                    }
                    .section {
                        margin: 30px 0;
                        padding: 25px;
                        background: #ffffff;
                        border-radius: 16px;
                        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.1);
                        border: 1px solid #e2e8f0;
                        backdrop-filter: blur(10px);
                        transition: transform 0.2s ease, box-shadow 0.2s ease;
                    }
                    .section:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08);
                    }
                    .gauge-container {
                        display: flex;
                        justify-content: space-around;
                        align-items: center;
                        flex-wrap: wrap;
                    }
                    h1 {
                        text-align: center;
                        color: #1e293b;
                        font-weight: 600;
                        font-size: 2.5rem;
                        margin-bottom: 10px;
                        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        background-clip: text;
                    }
                    h2 {
                        text-align: center;
                        color: #64748b;
                        font-weight: 500;
                        font-size: 1.8rem;
                        margin-bottom: 20px;
                    }
                </style>
            </head>
            <body>
                <h1>deqing-028 - 性能分析报告</h1>

                <div class="section">
                    <h2>总体性能指标</h2>
                    
        <div id="gauge-container-deqing-028" style="width: 100%; height: 400px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: #2c3e50; margin-bottom: 15px;">
                    deqing-028 - 总体性能仪表盘
                </h3>
            </div>
            <div id="gauge-chart-deqing-028" style="width: 100%; height: 350px;"></div>
        </div>

        <script>
            // 初始化仪表盘
            var gaugeChart_deqing_028 = echarts.init(document.getElementById('gauge-chart-deqing-028'));

            var gaugeOption_deqing_028 = {
                backgroundColor: 'rgba(255, 255, 255, 0.5)',
                series: [
                    
            {
                name: '检出率',
                type: 'gauge',
                center: ['25%', '50%'],
                radius: '70%',
                min: 0,
                max: 100,
                splitNumber: 10,
                axisLine: {
                    lineStyle: {
                        color: [[0.3, '#e67e22'], [0.7, '#3498db'], [1, '#3498db']],
                        width: 8
                    }
                },
                pointer: {
                    itemStyle: { color: 'auto' }
                },
                axisTick: {
                    distance: -30,
                    length: 8,
                    lineStyle: { color: '#fff', width: 2 }
                },
                splitLine: {
                    distance: -30,
                    length: 30,
                    lineStyle: { color: '#fff', width: 4 }
                },
                axisLabel: {
                    color: 'auto',
                    distance: 40,
                    fontSize: 12
                },
                detail: {
                    valueAnimation: true,
                    formatter: '{value}%',
                    color: '#3498db',
                    fontSize: 16,
                    offsetCenter: [0, '70%']
                },
                title: {
                    offsetCenter: [0, '90%'],
                    fontSize: 14,
                    color: '#5a6c7d'
                },
                data: [{ value: 64.6, name: '检出率' }]
            }
            ,
                    
            {
                name: '准确率',
                type: 'gauge',
                center: ['75%', '50%'],
                radius: '70%',
                min: 0,
                max: 100,
                splitNumber: 10,
                axisLine: {
                    lineStyle: {
                        color: [[0.3, '#e67e22'], [0.7, '#f39c12'], [1, '#2ecc71']],
                        width: 8
                    }
                },
                pointer: {
                    itemStyle: { color: 'auto' }
                },
                axisTick: {
                    distance: -30,
                    length: 8,
                    lineStyle: { color: '#fff', width: 2 }
                },
                splitLine: {
                    distance: -30,
                    length: 30,
                    lineStyle: { color: '#fff', width: 4 }
                },
                axisLabel: {
                    color: 'auto',
                    distance: 40,
                    fontSize: 12
                },
                detail: {
                    valueAnimation: true,
                    formatter: '{value}%',
                    color: '#2ecc71',
                    fontSize: 16,
                    offsetCenter: [0, '70%']
                },
                title: {
                    offsetCenter: [0, '90%'],
                    fontSize: 14,
                    color: '#5a6c7d'
                },
                data: [{ value: 83.7, name: '准确率' }]
            }
            
                ]
            };

            gaugeChart_deqing_028.setOption(gaugeOption_deqing_028);
            window.addEventListener('resize', () => gaugeChart_deqing_028.resize());
        </script>
        
                </div>

                <div class="section">
                    <h2>距离段性能对比</h2>
                    
        <div id="bar-chart-deqing_028" style="width: 1000px; height: 500px; margin: 20px auto;"></div>

        <script>
            // 初始化柱状图
            var barChart_deqing_028 = echarts.init(document.getElementById('bar-chart-deqing_028'));

            var barOption_deqing_028 = {
  "backgroundColor": "#f8fafc",
  "title": {
    "text": "deqing-028 - 距离段性能对比",
    "subtext": "不同目标类别在各距离段的准确率和检出率对比",
    "textStyle": {
      "color": "#1e293b",
      "fontSize": 16
    },
    "subtextStyle": {
      "color": "#64748b",
      "fontSize": 12
    }
  },
  "tooltip": {
    "trigger": "axis",
    "backgroundColor": "rgba(45, 45, 45, 0.95)",
    "borderColor": "#e2e8f0",
    "textStyle": {
      "color": "#ffffff"
    }
  },
  "legend": {
    "top": "8%",
    "textStyle": {
      "color": "#1e293b"
    }
  },
  "xAxis": {
    "type": "category",
    "data": [
      "0-50米",
      "50-100米",
      "总体"
    ],
    "name": "距离段",
    "axisLabel": {
      "color": "#1e293b"
    },
    "axisLine": {
      "lineStyle": {
        "color": "#e2e8f0"
      }
    }
  },
  "yAxis": {
    "type": "value",
    "name": "性能指标 (%)",
    "min": 0,
    "max": 100,
    "axisLabel": {
      "color": "#1e293b"
    },
    "axisLine": {
      "lineStyle": {
        "color": "#e2e8f0"
      }
    },
    "splitLine": {
      "show": true,
      "lineStyle": {
        "color": "#e2e8f0",
        "type": "dashed"
      }
    }
  },
  "series": [
    {
      "name": "机动车-准确率",
      "type": "bar",
      "data": [
        87.1,
        75.6,
        81.3
      ],
      "itemStyle": {
        "color": "#10b981"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "机动车-检出率",
      "type": "bar",
      "data": [
        66.7,
        59.5,
        63.1
      ],
      "itemStyle": {
        "color": "#6ee7b7"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "行人-准确率",
      "type": "bar",
      "data": [
        96.3,
        89.2,
        93.2
      ],
      "itemStyle": {
        "color": "#3b82f6"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "行人-检出率",
      "type": "bar",
      "data": [
        78.6,
        65.2,
        72.4
      ],
      "itemStyle": {
        "color": "#93c5fd"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "非机动车-准确率",
      "type": "bar",
      "data": [
        82.4,
        71.2,
        77.0
      ],
      "itemStyle": {
        "color": "#f59e0b"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "非机动车-检出率",
      "type": "bar",
      "data": [
        63.0,
        54.0,
        58.6
      ],
      "itemStyle": {
        "color": "#fbbf24"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    }
  ]
};

            barChart_deqing_028.setOption(barOption_deqing_028);
            window.addEventListener('resize', () => barChart_deqing_028.resize());
        </script>
        
                </div>

                <div class="section">
                    <h2>雷达图分析</h2>
                    
        <style>
            .radar-btn:hover {
                background: #3b82f6 !important;
                color: white !important;
                border-color: #3b82f6 !important;
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(59,130,246,0.3) !important;
            }
            .radar-active {
                background: #3b82f6 !important;
                color: white !important;
                border-color: #3b82f6 !important;
                box-shadow: 0 4px 12px rgba(59,130,246,0.3) !important;
            }
        </style>
        <div id="radar-container-deqing-028" style="width: 100%; height: 600px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: #1e293b; margin-bottom: 15px;">
                    deqing-028 - 雷达图分析
                </h3>
                <div id="radar-buttons-deqing-028" style="margin-bottom: 20px;">
        
                    <button onclick="switchRadar_deqing_028('0-50米', this)"
                            class="radar-btn radar-active"
                            style="margin: 0 10px; padding: 10px 20px;
                                   background: #ffffff;
                                   color: #1e293b;
                                   border: 2px solid #e2e8f0;
                                   border-radius: 8px; cursor: pointer;
                                   font-weight: 500; transition: all 0.2s ease;
                                   box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        0-50米
                    </button>
            
                    <button onclick="switchRadar_deqing_028('50-100米', this)"
                            class="radar-btn "
                            style="margin: 0 10px; padding: 10px 20px;
                                   background: #ffffff;
                                   color: #1e293b;
                                   border: 2px solid #e2e8f0;
                                   border-radius: 8px; cursor: pointer;
                                   font-weight: 500; transition: all 0.2s ease;
                                   box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        50-100米
                    </button>
            
                    <button onclick="switchRadar_deqing_028('总体', this)"
                            class="radar-btn "
                            style="margin: 0 10px; padding: 10px 20px;
                                   background: #ffffff;
                                   color: #1e293b;
                                   border: 2px solid #e2e8f0;
                                   border-radius: 8px; cursor: pointer;
                                   font-weight: 500; transition: all 0.2s ease;
                                   box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        总体
                    </button>
            
                </div>
            </div>
            <div id="radar-chart-deqing-028" style="width: 100%; height: 500px;"></div>
        </div>

        <script>
            // 雷达图数据
            var radarData_deqing_028 = {"0-50米": [87.13910761154857, 66.66666666666666, 96.28712871287128, 78.58585858585857, 82.4468085106383, 63.00813008130082], "50-100米": [75.57840616966581, 59.51417004048582, 89.20634920634922, 65.19721577726219, 71.22905027932961, 54.02542372881356], "总体": [81.2987012987013, 63.10483870967742, 93.18497913769124, 72.35421166306696, 76.97547683923706, 58.60995850622407]};

            // 雷达图配置
            var radarOption_deqing_028 = {
                backgroundColor: '#f8fafc',
                radar: {
                    indicator: [
                        {name: '人-准确率', max: 100},
                        {name: '人-检出率', max: 100},
                        {name: '车-准确率', max: 100},
                        {name: '车-检出率', max: 100},
                        {name: '非-准确率', max: 100},
                        {name: '非-检出率', max: 100}
                    ],
                    center: ['50%', '50%'],
                    radius: '70%',
                    axisName: {
                        color: '#1e293b',
                        fontSize: 12
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#e2e8f0'
                        }
                    },
                    splitArea: {
                        show: false
                    }
                },
                series: [{
                    type: 'radar',
                    data: [{
                        value: radarData_deqing_028['0-50米'],
                        name: '0-50米',
                        areaStyle: {
                            color: 'rgba(0, 212, 255, 0.3)'
                        },
                        lineStyle: {
                            color: '#3b82f6'
                        }
                    }]
                }]
            };

            // 初始化雷达图
            var radarChart_deqing_028 = echarts.init(document.getElementById('radar-chart-deqing-028'));
            radarChart_deqing_028.setOption(radarOption_deqing_028);

            // 切换函数
            function switchRadar_deqing_028(distBin, clickedButton) {
                // 更新按钮状态
                var buttons = document.querySelectorAll('#radar-buttons-deqing-028 .radar-btn');
                buttons.forEach(btn => btn.classList.remove('radar-active'));
                clickedButton.classList.add('radar-active');

                // 更新雷达图数据
                radarOption_deqing_028.series[0].data[0].value = radarData_deqing_028[distBin];
                radarOption_deqing_028.series[0].data[0].name = distBin;
                radarChart_deqing_028.setOption(radarOption_deqing_028);
            }
        </script>

        <style>
            .radar-btn.radar-active {
                background: #3b82f6 !important;
                color: #000000 !important;
            }
            .radar-btn:hover {
                background: #3b82f6 !important;
                color: #000000 !important;
            }
        </style>
        
                </div>

                <div class="section">
                    <h2>时间趋势分析</h2>
                    
        <style>
            .trend-btn:hover {
                background: #3b82f6 !important;
                color: white !important;
                border-color: #3b82f6 !important;
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(59,130,246,0.3) !important;
            }
            .trend-active {
                background: #3b82f6 !important;
                color: white !important;
                border-color: #3b82f6 !important;
                box-shadow: 0 4px 12px rgba(59,130,246,0.3) !important;
            }
        </style>
        <div id="trend-container-deqing-028" style="width: 100%; height: 700px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: #1e293b; margin-bottom: 15px;">
                    deqing-028 - 时间趋势分析
                </h3>
                <div id="trend-buttons-deqing-028" style="margin-bottom: 20px;">
        
                    <button onclick="switchTrend_deqing_028('0-50米', this)"
                            class="trend-btn trend-active"
                            style="margin: 0 10px; padding: 10px 20px;
                                   background: #ffffff;
                                   color: #1e293b;
                                   border: 2px solid #e2e8f0;
                                   border-radius: 8px; cursor: pointer;
                                   font-weight: 500; transition: all 0.2s ease;
                                   box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        0-50米
                    </button>
            
                    <button onclick="switchTrend_deqing_028('50-100米', this)"
                            class="trend-btn "
                            style="margin: 0 10px; padding: 10px 20px;
                                   background: #ffffff;
                                   color: #1e293b;
                                   border: 2px solid #e2e8f0;
                                   border-radius: 8px; cursor: pointer;
                                   font-weight: 500; transition: all 0.2s ease;
                                   box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        50-100米
                    </button>
            
                    <button onclick="switchTrend_deqing_028('总体', this)"
                            class="trend-btn "
                            style="margin: 0 10px; padding: 10px 20px;
                                   background: #ffffff;
                                   color: #1e293b;
                                   border: 2px solid #e2e8f0;
                                   border-radius: 8px; cursor: pointer;
                                   font-weight: 500; transition: all 0.2s ease;
                                   box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        总体
                    </button>
            
                </div>
            </div>
            <div id="trend-chart-deqing-028" style="width: 100%; height: 600px;"></div>
        </div>

        <script>
            // 时间戳数据
            var timestamps_deqing_028 = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76];

            // 趋势图数据
            var trendData_deqing_028 = {"0-50米": {"机动车-准确率": [100.0, 83.3, 100.0, 83.3, 100.0, 100.0, 100.0, 71.4, 80.0, 100.0, 100.0, 70.0, 100.0, 75.0, 70.0, 85.7, 100.0, 71.4, 75.0, 100.0, 100.0, 100.0, 70.0, 100.0, 100.0, 71.4, 83.3, 100.0, 100.0, 100.0, 85.7, 100.0, 83.3, 83.3, 100.0, 100.0, 87.5, 75.0, 87.5, 100.0, 80.0, 100.0, 100.0, 85.7, 85.7, 75.0, 85.7, 100.0, 100.0, 80.0, 100.0, 100.0, 100.0, 66.7, 100.0, 100.0, 100.0, 83.3, 80.0, 100.0, 100.0, 100.0, 88.9, 100.0, 100.0, 83.3, 100.0, 100.0, 77.8, 75.0, 83.3, 83.3, 75.0, 100.0, 100.0], "机动车-检出率": [66.7, 71.4, 60.0, 71.4, 62.5, 50.0, 71.4, 62.5, 66.7, 60.0, 66.7, 77.8, 60.0, 85.7, 77.8, 66.7, 60.0, 71.4, 60.0, 60.0, 33.3, 66.7, 70.0, 57.1, 66.7, 83.3, 62.5, 50.0, 55.6, 50.0, 60.0, 60.0, 62.5, 71.4, 77.8, 75.0, 87.5, 60.0, 70.0, 83.3, 66.7, 66.7, 62.5, 60.0, 60.0, 60.0, 75.0, 57.1, 33.3, 66.7, 66.7, 60.0, 75.0, 85.7, 66.7, 77.8, 83.3, 71.4, 66.7, 66.7, 50.0, 50.0, 80.0, 66.7, 60.0, 71.4, 57.1, 66.7, 77.8, 66.7, 83.3, 55.6, 50.0, 60.0, 66.7], "行人-准确率": [100.0, 83.3, 87.5, 100.0, 100.0, 87.5, 85.7, 83.3, 100.0, 100.0, 100.0, 83.3, 100.0, 100.0, 87.5, 88.9, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 83.3, 100.0, 100.0, 100.0, 100.0, 85.7, 100.0, 100.0, 88.9, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 85.7, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 90.0, 100.0, 100.0, 100.0, 100.0, 88.9, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 88.9, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0], "行人-检出率": [66.7, 71.4, 87.5, 88.9, 75.0, 77.8, 85.7, 83.3, 87.5, 85.7, 77.8, 83.3, 66.7, 75.0, 87.5, 80.0, 71.4, 70.0, 77.8, 80.0, 80.0, 80.0, 77.8, 75.0, 85.7, 83.3, 75.0, 75.0, 87.5, 75.0, 75.0, 88.9, 85.7, 80.0, 66.7, 80.0, 75.0, 66.7, 66.7, 77.8, 85.7, 66.7, 66.7, 75.0, 88.9, 88.9, 66.7, 66.7, 75.0, 66.7, 66.7, 71.4, 71.4, 90.0, 70.0, 87.5, 80.0, 66.7, 88.9, 75.0, 70.0, 66.7, 80.0, 80.0, 71.4, 80.0, 88.9, 75.0, 75.0, 66.7, 66.7, 88.9, 75.0, 80.0, 80.0], "非机动车-准确率": [100.0, 80.0, 87.5, 100.0, 100.0, 75.0, 77.8, 100.0, 80.0, 71.4, 77.8, 66.7, 100.0, 100.0, 100.0, 66.7, 80.0, 100.0, 85.7, 75.0, 75.0, 100.0, 66.7, 80.0, 100.0, 66.7, 100.0, 83.3, 80.0, 100.0, 80.0, 100.0, 100.0, 100.0, 100.0, 85.7, 83.3, 66.7, 100.0, 100.0, 100.0, 75.0, 80.0, 71.4, 100.0, 100.0, 100.0, 85.7, 88.9, 83.3, 80.0, 100.0, 66.7, 100.0, 75.0, 63.6, 100.0, 100.0, 87.5, 80.0, 83.3, 100.0, 100.0, 75.0, 75.0, 71.4, 100.0, 85.7, 80.0, 62.5, 100.0, 100.0, 80.0, 66.7, 75.0], "非机动车-检出率": [50.0, 66.7, 77.8, 66.7, 66.7, 66.7, 77.8, 50.0, 50.0, 55.6, 70.0, 80.0, 50.0, 66.7, 33.3, 66.7, 57.1, 66.7, 60.0, 75.0, 75.0, 75.0, 66.7, 66.7, 60.0, 66.7, 50.0, 50.0, 57.1, 66.7, 66.7, 60.0, 66.7, 57.1, 55.6, 66.7, 83.3, 66.7, 70.0, 50.0, 66.7, 60.0, 66.7, 55.6, 66.7, 60.0, 33.3, 60.0, 80.0, 71.4, 66.7, 50.0, 66.7, 50.0, 60.0, 77.8, 66.7, 50.0, 70.0, 57.1, 55.6, 60.0, 33.3, 60.0, 60.0, 71.4, 60.0, 75.0, 66.7, 62.5, 83.3, 42.9, 66.7, 50.0, 60.0]}, "50-100米": {"机动车-准确率": [100.0, 63.6, 100.0, 80.0, 54.5, 75.0, 100.0, 71.4, 100.0, 61.5, 57.1, 100.0, 60.0, 66.7, 66.7, 71.4, 100.0, 80.0, 100.0, 80.0, 75.0, 66.7, 100.0, 100.0, 87.5, 100.0, 100.0, 100.0, 100.0, 75.0, 75.0, 100.0, 80.0, 63.6, 83.3, 60.0, 75.0, 100.0, 80.0, 100.0, 66.7, 66.7, 75.0, 57.1, 66.7, 85.7, 83.3, 66.7, 100.0, 63.6, 100.0, 55.6, 87.5, 75.0, 83.3, 60.0, 60.0, 66.7, 71.4, 100.0, 66.7, 100.0, 63.6, 66.7, 100.0, 85.7, 77.8, 100.0, 100.0, 100.0, 100.0, 66.7, 75.0, 66.7, 75.0], "机动车-检出率": [60.0, 70.0, 80.0, 80.0, 75.0, 50.0, 40.0, 55.6, 66.7, 80.0, 57.1, 42.9, 50.0, 50.0, 50.0, 62.5, 50.0, 44.4, 50.0, 80.0, 50.0, 66.7, 33.3, 66.7, 70.0, 40.0, 50.0, 44.4, 66.7, 50.0, 60.0, 66.7, 50.0, 77.8, 71.4, 60.0, 50.0, 37.5, 66.7, 33.3, 66.7, 33.3, 60.0, 66.7, 44.4, 75.0, 50.0, 75.0, 50.0, 77.8, 55.6, 50.0, 70.0, 60.0, 55.6, 60.0, 75.0, 66.7, 55.6, 50.0, 66.7, 60.0, 70.0, 80.0, 60.0, 75.0, 70.0, 33.3, 25.0, 60.0, 42.9, 50.0, 60.0, 57.1, 60.0], "行人-准确率": [100.0, 100.0, 100.0, 80.0, 85.7, 83.3, 100.0, 75.0, 75.0, 83.3, 80.0, 100.0, 100.0, 100.0, 100.0, 75.0, 100.0, 100.0, 100.0, 100.0, 75.0, 100.0, 100.0, 80.0, 100.0, 100.0, 100.0, 83.3, 100.0, 100.0, 100.0, 100.0, 85.7, 100.0, 100.0, 85.7, 100.0, 85.7, 100.0, 100.0, 100.0, 83.3, 100.0, 100.0, 80.0, 100.0, 75.0, 66.7, 80.0, 75.0, 100.0, 66.7, 100.0, 83.3, 100.0, 83.3, 100.0, 100.0, 100.0, 100.0, 100.0, 83.3, 100.0, 100.0, 100.0, 100.0, 75.0, 100.0, 100.0, 83.3, 100.0, 100.0, 100.0, 80.0, 66.7], "行人-检出率": [50.0, 66.7, 60.0, 57.1, 75.0, 83.3, 66.7, 60.0, 85.7, 62.5, 66.7, 80.0, 70.0, 66.7, 60.0, 66.7, 75.0, 33.3, 50.0, 71.4, 60.0, 66.7, 33.3, 66.7, 50.0, 60.0, 75.0, 62.5, 50.0, 60.0, 66.7, 57.1, 85.7, 75.0, 66.7, 60.0, 33.3, 75.0, 70.0, 50.0, 71.4, 62.5, 83.3, 50.0, 66.7, 50.0, 60.0, 66.7, 80.0, 75.0, 66.7, 80.0, 75.0, 62.5, 60.0, 62.5, 62.5, 50.0, 71.4, 33.3, 33.3, 62.5, 80.0, 71.4, 75.0, 66.7, 75.0, 66.7, 66.7, 62.5, 66.7, 60.0, 66.7, 57.1, 50.0], "非机动车-准确率": [87.5, 75.0, 75.0, 83.3, 66.7, 100.0, 75.0, 50.0, 55.6, 100.0, 100.0, 50.0, 66.7, 100.0, 57.1, 100.0, 100.0, 100.0, 80.0, 66.7, 66.7, 71.4, 83.3, 66.7, 55.6, 100.0, 100.0, 50.0, 66.7, 100.0, 57.1, 66.7, 50.0, 100.0, 70.0, 75.0, 75.0, 75.0, 83.3, 87.5, 60.0, 50.0, 66.7, 71.4, 57.1, 66.7, 66.7, 71.4, 62.5, 100.0, 100.0, 62.5, 83.3, 100.0, 100.0, 100.0, 66.7, 100.0, 50.0, 100.0, 100.0, 66.7, 83.3, 85.7, 80.0, 100.0, 75.0, 100.0, 62.5, 75.0, 100.0, 83.3, 66.7, 58.3, 50.0], "非机动车-检出率": [70.0, 66.7, 42.9, 55.6, 50.0, 33.3, 50.0, 44.4, 55.6, 40.0, 50.0, 40.0, 66.7, 57.1, 44.4, 33.3, 33.3, 66.7, 50.0, 66.7, 50.0, 62.5, 62.5, 50.0, 62.5, 40.0, 33.3, 60.0, 40.0, 40.0, 66.7, 57.1, 66.7, 33.3, 70.0, 60.0, 60.0, 42.9, 55.6, 70.0, 60.0, 66.7, 57.1, 50.0, 66.7, 44.4, 66.7, 50.0, 55.6, 33.3, 50.0, 71.4, 55.6, 25.0, 40.0, 50.0, 57.1, 33.3, 75.0, 33.3, 25.0, 40.0, 71.4, 66.7, 66.7, 40.0, 50.0, 40.0, 50.0, 60.0, 40.0, 50.0, 50.0, 70.0, 66.7]}, "总体": {"机动车-准确率": [100.0, 73.5, 100.0, 81.7, 77.3, 87.5, 100.0, 71.4, 90.0, 80.8, 78.6, 85.0, 80.0, 70.8, 68.3, 78.6, 100.0, 75.7, 87.5, 90.0, 87.5, 83.3, 85.0, 100.0, 93.8, 85.7, 91.7, 100.0, 100.0, 87.5, 80.4, 100.0, 81.7, 73.5, 91.7, 80.0, 81.2, 87.5, 83.8, 100.0, 73.3, 83.3, 87.5, 71.4, 76.2, 80.4, 84.5, 83.3, 100.0, 71.8, 100.0, 77.8, 93.8, 70.8, 91.7, 80.0, 80.0, 75.0, 75.7, 100.0, 83.3, 100.0, 76.3, 83.3, 100.0, 84.5, 88.9, 100.0, 88.9, 87.5, 91.7, 75.0, 75.0, 83.3, 87.5], "机动车-检出率": [63.3, 70.7, 70.0, 75.7, 68.8, 50.0, 55.7, 59.0, 66.7, 70.0, 61.9, 60.3, 55.0, 67.9, 63.9, 64.6, 55.0, 57.9, 55.0, 70.0, 41.7, 66.7, 51.7, 61.9, 68.3, 61.7, 56.2, 47.2, 61.1, 50.0, 60.0, 63.3, 56.2, 74.6, 74.6, 67.5, 68.8, 48.8, 68.3, 58.3, 66.7, 50.0, 61.3, 63.3, 52.2, 67.5, 62.5, 66.1, 41.7, 72.2, 61.1, 55.0, 72.5, 72.9, 61.1, 68.9, 79.2, 69.0, 61.1, 58.3, 58.3, 55.0, 75.0, 73.3, 60.0, 73.2, 63.6, 50.0, 51.4, 63.3, 63.1, 52.8, 55.0, 58.6, 63.3], "行人-准确率": [100.0, 91.7, 93.8, 90.0, 92.9, 85.4, 92.9, 79.2, 87.5, 91.7, 90.0, 91.7, 100.0, 100.0, 93.8, 81.9, 100.0, 100.0, 100.0, 100.0, 87.5, 100.0, 100.0, 90.0, 100.0, 91.7, 100.0, 91.7, 100.0, 100.0, 92.9, 100.0, 92.9, 94.4, 100.0, 92.9, 100.0, 92.9, 100.0, 100.0, 92.9, 91.7, 100.0, 100.0, 90.0, 100.0, 87.5, 83.3, 90.0, 87.5, 100.0, 83.3, 100.0, 86.7, 100.0, 91.7, 100.0, 100.0, 94.4, 100.0, 100.0, 91.7, 100.0, 100.0, 100.0, 100.0, 81.9, 100.0, 100.0, 91.7, 100.0, 100.0, 100.0, 90.0, 83.3], "行人-检出率": [58.3, 69.0, 73.8, 73.0, 75.0, 80.6, 76.2, 71.7, 86.6, 74.1, 72.2, 81.7, 68.3, 70.8, 73.8, 73.3, 73.2, 51.7, 63.9, 75.7, 70.0, 73.3, 55.6, 70.8, 67.9, 71.7, 75.0, 68.8, 68.8, 67.5, 70.8, 73.0, 85.7, 77.5, 66.7, 70.0, 54.2, 70.8, 68.3, 63.9, 78.6, 64.6, 75.0, 62.5, 77.8, 69.4, 63.3, 66.7, 77.5, 70.8, 66.7, 75.7, 73.2, 76.2, 65.0, 75.0, 71.2, 58.3, 80.2, 54.2, 51.7, 64.6, 80.0, 75.7, 73.2, 73.3, 81.9, 70.8, 70.8, 64.6, 66.7, 74.4, 70.8, 68.6, 65.0], "非机动车-准确率": [93.8, 77.5, 81.2, 91.7, 83.3, 87.5, 76.4, 75.0, 67.8, 85.7, 88.9, 58.3, 83.3, 100.0, 78.6, 83.3, 90.0, 100.0, 82.9, 70.8, 70.8, 85.7, 75.0, 73.3, 77.8, 83.3, 100.0, 66.7, 73.3, 100.0, 68.6, 83.3, 75.0, 100.0, 85.0, 80.4, 79.2, 70.8, 91.7, 93.8, 80.0, 62.5, 73.3, 71.4, 78.6, 83.3, 83.3, 78.6, 75.7, 91.7, 90.0, 81.2, 75.0, 100.0, 87.5, 81.8, 83.3, 100.0, 68.8, 90.0, 91.7, 83.3, 91.7, 80.4, 77.5, 85.7, 87.5, 92.9, 71.2, 68.8, 100.0, 91.7, 73.3, 62.5, 62.5], "非机动车-检出率": [60.0, 66.7, 60.3, 61.1, 58.3, 50.0, 63.9, 47.2, 52.8, 47.8, 60.0, 60.0, 58.3, 61.9, 38.9, 50.0, 45.2, 66.7, 55.0, 70.8, 62.5, 68.8, 64.6, 58.3, 61.3, 53.3, 41.7, 55.0, 48.6, 53.3, 66.7, 58.6, 66.7, 45.2, 62.8, 63.3, 71.7, 54.8, 62.8, 60.0, 63.3, 63.3, 61.9, 52.8, 66.7, 52.2, 50.0, 55.0, 67.8, 52.4, 58.3, 60.7, 61.1, 37.5, 50.0, 63.9, 61.9, 41.7, 72.5, 45.2, 40.3, 50.0, 52.4, 63.3, 63.3, 55.7, 55.0, 57.5, 58.3, 61.3, 61.7, 46.4, 58.3, 60.0, 63.3]}};

            // 颜色配置
            var categoryColors_deqing_028 = {
                '行人': ['#3b82f6', '#93c5fd'],
                '机动车': ['#10b981', '#6ee7b7'],
                '非机动车': ['#f59e0b', '#fbbf24']
            };

            // 趋势图配置
            function getTrendOption_deqing_028(distBin) {
                var seriesData = trendData_deqing_028[distBin];
                var series = [];

                Object.keys(seriesData).forEach(function(seriesName) {
                    var category = seriesName.split('-')[0];
                    var metric = seriesName.split('-')[1];
                    var colorIndex = metric === '准确率' ? 0 : 1;
                    var lineType = metric === '准确率' ? 'solid' : 'dashed';

                    series.push({
                        name: seriesName,
                        type: 'line',
                        data: seriesData[seriesName],
                        smooth: false,
                        symbol: 'none',
                        lineStyle: {
                            color: categoryColors_deqing_028[category][colorIndex],
                            width: 3,
                            type: lineType
                        },
                        emphasis: {
                            focus: 'series',
                            lineStyle: {
                                width: 4
                            }
                        }
                    });
                });

                return {
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    title: {
                        text: distBin + ' - 性能趋势',
                        left: 'center',
                        textStyle: {
                            color: '#5a6c7d',
                            fontSize: 16
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        borderColor: '#bdc3c7',
                        textStyle: {
                            color: '#2c3e50'
                        }
                    },
                    legend: {
                        top: '8%',
                        textStyle: {
                            color: '#5a6c7d'
                        }
                    },
                    grid: {
                        left: '10%',
                        right: '10%',
                        top: '20%',
                        bottom: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: timestamps_deqing_028,
                        name: '帧号',
                        axisLabel: {
                            color: '#7f8c8d'
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#bdc3c7'
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '性能指标 (%)',
                        min: 0,
                        max: 100,
                        axisLabel: {
                            color: '#7f8c8d'
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#bdc3c7'
                            }
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#ecf0f1',
                                type: 'dashed'
                            }
                        }
                    },
                    series: series
                };
            }

            // 初始化趋势图
            var trendChart_deqing_028 = echarts.init(document.getElementById('trend-chart-deqing-028'));
            trendChart_deqing_028.setOption(getTrendOption_deqing_028('0-50米'));

            // 切换函数
            function switchTrend_deqing_028(distBin, clickedButton) {
                // 更新按钮状态
                var buttons = document.querySelectorAll('#trend-buttons-deqing-028 .trend-btn');
                buttons.forEach(btn => btn.classList.remove('trend-active'));
                clickedButton.classList.add('trend-active');

                // 更新趋势图
                trendChart_deqing_028.setOption(getTrendOption_deqing_028(distBin));
            }
        </script>

        <style>
            .trend-btn.trend-active {
                background: #3b82f6 !important;
                color: #000000 !important;
            }
            .trend-btn:hover {
                background: #3b82f6 !important;
                color: #000000 !important;
            }
        </style>
        
                </div>
            </body>
            </html>
            