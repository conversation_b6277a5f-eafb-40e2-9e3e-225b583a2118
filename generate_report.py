#!/usr/bin/env python3
"""
数据分析可视化报告生成器
CLI入口，整合所有模块，生成多页HTML报告
"""

import argparse
import os
import sys
import logging
from pathlib import Path
from typing import Dict, Any
import zipfile
from datetime import datetime

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.loader.excel_loader import ExcelDataLoader, load_config
from src.metrics.calculator import MetricsCalculator
from src.echarts.theme import create_theme
from src.echarts.charts_single import SingleSceneCharts
from src.echarts.charts_master import MasterSceneCharts

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ReportGenerator:
    """报告生成器主类"""
    
    def __init__(self, config_path: str):
        """
        初始化报告生成器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = load_config(config_path)
        if not self.config:
            raise ValueError(f"无法加载配置文件: {config_path}")
            
        self.loader = ExcelDataLoader(self.config)
        self.calculator = MetricsCalculator(self.config)
        self.theme = create_theme(self.config)
        self.single_charts = SingleSceneCharts(self.theme)
        self.master_charts = MasterSceneCharts(self.theme)
        
        # 报告配置
        self.report_config = self.config.get('report', {})
        self.report_title = self.report_config.get('title', '多场景检测性能分析报告')
        self.report_subtitle = self.report_config.get('subtitle', '基于原生ECharts的静态HTML报告')
        self.report_author = self.report_config.get('author', 'AI助手')
        
    def load_and_process_data(self, data_dir: str) -> Dict[str, Any]:
        """
        加载和处理数据
        
        Args:
            data_dir: 数据目录路径
            
        Returns:
            处理结果字典
        """
        logger.info("开始加载和处理数据...")
        
        # 加载Excel数据
        df = self.loader.load_excel_files(data_dir)
        df = self.loader.clean_and_validate_data(df)
        
        # 计算指标和聚合
        results = self.calculator.process_all_aggregations(df)
        
        logger.info("数据加载和处理完成")
        return results
    
    def generate_master_report(self, results: Dict[str, Any], output_dir: str) -> str:
        """
        生成全场景总览报告

        Args:
            results: 数据处理结果
            output_dir: 输出目录

        Returns:
            生成的HTML文件路径
        """
        logger.info("生成全场景总览报告...")

        # 生成全场景HTML页面
        master_html = self.master_charts.generate_master_page(results)

        # 输出文件路径
        output_path = os.path.join(output_dir, "index.html")

        # 写入HTML文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(master_html)

        logger.info(f"全场景总览报告已生成: {output_path}")
        return output_path
    
    def generate_single_scene_reports(self, results: Dict[str, Any], output_dir: str) -> list:
        """
        生成单场景报告

        Args:
            results: 数据处理结果
            output_dir: 输出目录

        Returns:
            生成的HTML文件路径列表
        """
        logger.info("生成单场景报告...")

        scene_data = results['scene_distance_category']
        scenes = scene_data['scene_id'].unique()

        output_files = []

        for scene_id in scenes:
            scene_name = scene_data[scene_data['scene_id'] == scene_id]['scene_name'].iloc[0]
            scene_subset = scene_data[scene_data['scene_id'] == scene_id]

            # 获取该场景的原始时间序列数据用于时间趋势图
            raw_scene_data = results['raw_data'][results['raw_data']['scene_id'] == scene_id]

            logger.info(f"  生成场景 {scene_name} 的报告...")

            # 创建各种图表
            gauge_html = self.single_charts.create_gauge_charts(scene_subset, scene_name)
            bar_html = self.single_charts.create_grouped_bar_chart(scene_subset, scene_name)
            radar_html = self.single_charts.create_radar_chart_with_switch(scene_subset, scene_name)
            trend_html = self.single_charts.create_time_trend_with_switch(raw_scene_data, scene_name)

            # 生成完整HTML页面
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>{scene_name} - 性能分析报告</title>
                <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
                <style>
                    body {{
                        background: {self.theme.chart_theme.get('background_color', '#1e1e1e')};
                        color: {self.theme.chart_theme.get('text_color', '#ffffff')};
                        font-family: {self.theme.chart_theme.get('font_family', 'Microsoft YaHei, Arial, sans-serif')};
                        margin: 0;
                        padding: 20px;
                        min-height: 100vh;
                    }}
                    .section {{
                        margin: 30px 0;
                        padding: 25px;
                        background: {self.theme.chart_theme.get('secondary_bg', '#2d2d2d')};
                        border-radius: 12px;
                        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                        border: 1px solid {self.theme.chart_theme.get('grid_color', '#404040')};
                    }}
                    .gauge-container {{
                        display: flex;
                        justify-content: space-around;
                        align-items: center;
                        flex-wrap: wrap;
                    }}
                    h1 {{
                        text-align: center;
                        color: {self.theme.chart_theme.get('text_color', '#ffffff')};
                        font-weight: 300;
                        font-size: 2.5rem;
                        margin-bottom: 10px;
                        text-shadow: 0 2px 4px rgba(0,0,0,0.5);
                    }}
                    h2 {{
                        text-align: center;
                        color: {self.theme.chart_theme.get('secondary_text', '#cccccc')};
                        font-weight: 400;
                        font-size: 1.8rem;
                        margin-bottom: 20px;
                    }}
                </style>
            </head>
            <body>
                <h1>{scene_name} - 性能分析报告</h1>

                <div class="section">
                    <h2>总体性能指标</h2>
                    {gauge_html}
                </div>

                <div class="section">
                    <h2>距离段性能对比</h2>
                    {bar_html}
                </div>

                <div class="section">
                    <h2>雷达图分析</h2>
                    {radar_html}
                </div>

                <div class="section">
                    <h2>时间趋势分析</h2>
                    {trend_html}
                </div>
            </body>
            </html>
            """

            # 输出文件路径
            output_path = os.path.join(output_dir, f"scene_{scene_id}.html")

            # 写入HTML文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            output_files.append(output_path)
            logger.info(f"  场景 {scene_name} 报告已生成: {output_path}")

        logger.info("所有单场景报告生成完成")
        return output_files
    
    def generate_summary_report(self, results: Dict[str, Any], output_dir: str) -> str:
        """
        生成汇总统计报告
        
        Args:
            results: 数据处理结果
            output_dir: 输出目录
            
        Returns:
            生成的HTML文件路径
        """
        logger.info("生成汇总统计报告...")
        
        stats = results['summary_stats']
        
        # 生成HTML内容
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>{self.report_title} - 统计汇总</title>
            <style>
                body {{ font-family: Microsoft YaHei, Arial, sans-serif; margin: 40px; }}
                .header {{ text-align: center; margin-bottom: 40px; }}
                .stats-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }}
                .stats-card {{ background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; }}
                .stats-card h3 {{ color: #333; margin-top: 0; }}
                .metric {{ display: flex; justify-content: space-between; margin: 10px 0; }}
                .metric-name {{ font-weight: bold; }}
                .metric-value {{ color: #5470C6; }}
                table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 12px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>{self.report_title}</h1>
                <h2>统计汇总</h2>
                <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p>作者: {self.report_author}</p>
            </div>
            
            <div class="stats-grid">
                <div class="stats-card">
                    <h3>总体统计</h3>
                    <div class="metric">
                        <span class="metric-name">总场景数:</span>
                        <span class="metric-value">{stats['total_scenes']}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-name">总距离段数:</span>
                        <span class="metric-value">{stats['total_distance_bins']}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-name">总类别数:</span>
                        <span class="metric-value">{stats['total_categories']}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-name">总样本数:</span>
                        <span class="metric-value">{int(stats['total_samples'])}</span>
                    </div>
                </div>
                
                <div class="stats-card">
                    <h3>总体性能</h3>
                    <div class="metric">
                        <span class="metric-name">总体检出率:</span>
                        <span class="metric-value">{stats['overall_recall']:.3f}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-name">总体准确率:</span>
                        <span class="metric-value">{stats['overall_precision']:.3f}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-name">总体F1分数:</span>
                        <span class="metric-value">{stats['overall_f1']:.3f}</span>
                    </div>
                </div>
            </div>
            
            <h3>按场景统计</h3>
            <table>
                <tr>
                    <th>场景</th>
                    <th>平均检出率</th>
                    <th>平均准确率</th>
                    <th>平均F1分数</th>
                    <th>样本数</th>
                </tr>
        """
        
        # 添加场景统计表格
        for scene, scene_stats in stats['by_scene'].items():
            html_content += f"""
                <tr>
                    <td>{scene}</td>
                    <td>{scene_stats['recall']:.3f}</td>
                    <td>{scene_stats['precision']:.3f}</td>
                    <td>{scene_stats['f1']:.3f}</td>
                    <td>{int(scene_stats['sample_cnt'])}</td>
                </tr>
            """
        
        html_content += """
            </table>
            
            <h3>按类别统计</h3>
            <table>
                <tr>
                    <th>目标类别</th>
                    <th>平均检出率</th>
                    <th>平均准确率</th>
                    <th>平均F1分数</th>
                    <th>样本数</th>
                </tr>
        """
        
        # 添加类别统计表格
        for category, cat_stats in stats['by_category'].items():
            html_content += f"""
                <tr>
                    <td>{category}</td>
                    <td>{cat_stats['recall']:.3f}</td>
                    <td>{cat_stats['precision']:.3f}</td>
                    <td>{cat_stats['f1']:.3f}</td>
                    <td>{int(cat_stats['sample_cnt'])}</td>
                </tr>
            """
        
        html_content += """
            </table>
        </body>
        </html>
        """
        
        # 输出文件路径
        output_path = os.path.join(output_dir, "summary.html")
        
        # 写入文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"汇总统计报告已生成: {output_path}")
        return output_path

    def create_navigation_index(self, output_dir: str, scene_files: list) -> str:
        """
        创建导航索引页面

        Args:
            output_dir: 输出目录
            scene_files: 场景文件列表

        Returns:
            导航页面路径
        """
        logger.info("创建导航索引页面...")

        # 提取场景信息
        scenes_info = []
        for file_path in scene_files:
            if file_path.endswith('.html') and 'scene_' in file_path:
                scene_id = os.path.basename(file_path).replace('scene_', '').replace('.html', '')
                scene_name = self.config.get('scene_mapping', {}).get(scene_id, scene_id)
                scenes_info.append((scene_id, scene_name, os.path.basename(file_path)))

        # 生成导航HTML
        nav_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>{self.report_title} - 导航</title>
            <style>
                body {{ font-family: Microsoft YaHei, Arial, sans-serif; margin: 40px; }}
                .header {{ text-align: center; margin-bottom: 40px; }}
                .nav-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }}
                .nav-card {{ background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; text-align: center; }}
                .nav-card:hover {{ background: #e9ecef; }}
                .nav-card a {{ text-decoration: none; color: #5470C6; font-weight: bold; font-size: 18px; }}
                .nav-card p {{ color: #666; margin: 10px 0; }}
                .main-links {{ text-align: center; margin: 40px 0; }}
                .main-links a {{ display: inline-block; margin: 10px 20px; padding: 15px 30px; background: #5470C6; color: white; text-decoration: none; border-radius: 5px; }}
                .main-links a:hover {{ background: #4c63d2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>{self.report_title}</h1>
                <h2>报告导航</h2>
                <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>

            <div class="main-links">
                <a href="index.html">📊 全场景总览</a>
                <a href="summary.html">📈 统计汇总</a>
            </div>

            <h3 style="text-align: center;">单场景报告</h3>
            <div class="nav-grid">
        """

        # 添加场景链接
        for scene_id, scene_name, filename in scenes_info:
            nav_html += f"""
                <div class="nav-card">
                    <a href="{filename}">{scene_name}</a>
                    <p>场景ID: {scene_id}</p>
                    <p>详细性能分析</p>
                </div>
            """

        nav_html += """
            </div>
        </body>
        </html>
        """

        # 输出文件路径
        nav_path = os.path.join(output_dir, "navigation.html")

        # 写入文件
        with open(nav_path, 'w', encoding='utf-8') as f:
            f.write(nav_html)

        logger.info(f"导航索引页面已生成: {nav_path}")
        return nav_path

    def create_zip_package(self, output_dir: str, zip_path: str = None) -> str:
        """
        打包报告为ZIP文件

        Args:
            output_dir: 输出目录
            zip_path: ZIP文件路径，如果为None则自动生成

        Returns:
            ZIP文件路径
        """
        if zip_path is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            zip_path = os.path.join(os.path.dirname(output_dir), f"report_{timestamp}.zip")

        logger.info(f"打包报告到: {zip_path}")

        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(output_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, output_dir)
                    zipf.write(file_path, arc_name)

        logger.info(f"报告已打包: {zip_path}")
        return zip_path

    def generate_full_report(self, data_dir: str, output_dir: str, create_zip: bool = False) -> Dict[str, str]:
        """
        生成完整报告

        Args:
            data_dir: 数据目录
            output_dir: 输出目录
            create_zip: 是否创建ZIP包

        Returns:
            生成的文件路径字典
        """
        logger.info("开始生成完整报告...")

        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 加载和处理数据
        results = self.load_and_process_data(data_dir)

        # 生成各种报告
        output_files = {}

        # 全场景总览
        output_files['master'] = self.generate_master_report(results, output_dir)

        # 单场景报告
        scene_files = self.generate_single_scene_reports(results, output_dir)
        output_files['scenes'] = scene_files

        # 统计汇总
        output_files['summary'] = self.generate_summary_report(results, output_dir)

        # 导航页面
        output_files['navigation'] = self.create_navigation_index(output_dir, scene_files)

        # 创建ZIP包
        if create_zip:
            output_files['zip'] = self.create_zip_package(output_dir)

        logger.info("完整报告生成完成!")
        return output_files


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='数据分析可视化报告生成器',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python generate_report.py --input data/ --output report/
  python generate_report.py --input data/ --output report/ --config config/config.yaml --zip
        """
    )

    parser.add_argument(
        '--input', '-i',
        required=True,
        help='输入数据目录路径'
    )

    parser.add_argument(
        '--output', '-o',
        required=True,
        help='输出报告目录路径'
    )

    parser.add_argument(
        '--config', '-c',
        default='config/config.yaml',
        help='配置文件路径 (默认: config/config.yaml)'
    )

    parser.add_argument(
        '--zip', '-z',
        action='store_true',
        help='生成ZIP压缩包'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='详细输出'
    )

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        # 检查输入目录
        if not os.path.exists(args.input):
            raise FileNotFoundError(f"输入目录不存在: {args.input}")

        # 检查配置文件
        if not os.path.exists(args.config):
            raise FileNotFoundError(f"配置文件不存在: {args.config}")

        # 创建报告生成器
        generator = ReportGenerator(args.config)

        # 生成报告
        output_files = generator.generate_full_report(
            data_dir=args.input,
            output_dir=args.output,
            create_zip=args.zip
        )

        # 输出结果
        print("\n" + "="*60)
        print("报告生成完成!")
        print("="*60)
        print(f"输出目录: {args.output}")
        print(f"主页面: {output_files['master']}")
        print(f"导航页面: {output_files['navigation']}")
        print(f"统计汇总: {output_files['summary']}")
        print(f"场景报告数: {len(output_files['scenes'])}")

        if 'zip' in output_files:
            print(f"ZIP包: {output_files['zip']}")

        print("\n建议从导航页面开始浏览报告。")

    except Exception as e:
        logger.error(f"报告生成失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
