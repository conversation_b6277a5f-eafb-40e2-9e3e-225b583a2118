
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>全场景性能分析报告</title>
            <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
            <style>
                body {
                    background: #1e1e1e;
                    color: #ffffff;
                    font-family: Microsoft YaHei, 'Segoe UI', Arial, sans-serif;
                    margin: 0;
                    padding: 20px;
                    min-height: 100vh;
                }
                .section {
                    margin: 30px 0;
                    padding: 25px;
                    background: #2d2d2d;
                    border-radius: 12px;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                    border: 1px solid #404040;
                }
                h1 {
                    text-align: center;
                    color: #ffffff;
                    font-weight: 300;
                    font-size: 2.5rem;
                    margin-bottom: 10px;
                    text-shadow: 0 2px 4px rgba(0,0,0,0.5);
                }
                h2 {
                    text-align: center;
                    color: #cccccc;
                    font-weight: 400;
                    font-size: 1.8rem;
                    margin-bottom: 20px;
                }
            </style>
        </head>
        <body>
            <h1>全场景性能分析报告</h1>

            <div class="section">
                <h2>总体概览</h2>
                
        <style>
            .summary-cards {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 25px;
                margin: 20px 0;
            }

            .summary-card {
                background: #2d2d2d;
                border-radius: 16px;
                padding: 25px;
                box-shadow: 0 6px 20px rgba(0,0,0,0.3);
                border-left: 4px solid;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
                border: 1px solid #404040;
            }

            .summary-card::before {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                width: 80px;
                height: 80px;
                background: linear-gradient(45deg, rgba(0,212,255,0.1), transparent);
                border-radius: 50%;
                transform: translate(25px, -25px);
            }

            .summary-card:hover {
                transform: translateY(-3px);
                box-shadow: 0 12px 30px rgba(0,0,0,0.4);
            }

            .summary-card.excellent { border-left-color: #2ecc71; }
            .summary-card.good { border-left-color: #3498db; }
            .summary-card.needs-improvement { border-left-color: #e67e22; }
            .summary-card.overall { border-left-color: #9b59b6; }

            .card-header {
                display: flex;
                align-items: center;
                margin-bottom: 15px;
            }

            .card-icon {
                font-size: 2.5rem;
                margin-right: 15px;
            }

            .card-title {
                font-size: 1.2rem;
                font-weight: 500;
                color: #ffffff;
            }

            .card-metrics {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 15px;
                margin-top: 20px;
            }

            .metric {
                text-align: center;
            }

            .metric-value {
                font-size: 1.8rem;
                font-weight: 600;
                margin-bottom: 5px;
            }

            .metric-label {
                font-size: 0.85rem;
                color: #cccccc;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                font-weight: 500;
            }
        </style>

        <div class="summary-cards">
            <div class="summary-card good">
                <div class="card-header">
                    <div class="card-icon">🎯</div>
                    <div class="card-title">总体检出率</div>
                </div>
                <div class="card-metrics">
                    <div class="metric">
                        <div class="metric-value" style="color: #3498db;">69.2%</div>
                        <div class="metric-label">平均值</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" style="color: #7f8c8d;">2</div>
                        <div class="metric-label">场景数</div>
                    </div>
                </div>
            </div>

            <div class="summary-card excellent">
                <div class="card-header">
                    <div class="card-icon">✅</div>
                    <div class="card-title">总体准确率</div>
                </div>
                <div class="card-metrics">
                    <div class="metric">
                        <div class="metric-value" style="color: #2ecc71;">84.4%</div>
                        <div class="metric-label">平均值</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" style="color: #7f8c8d;">2</div>
                        <div class="metric-label">场景数</div>
                    </div>
                </div>
            </div>

            <div class="summary-card good">
                <div class="card-header">
                    <div class="card-icon">⚖️</div>
                    <div class="card-title">总体F1分数</div>
                </div>
                <div class="card-metrics">
                    <div class="metric">
                        <div class="metric-value" style="color: #3498db;">76.0%</div>
                        <div class="metric-label">平均值</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" style="color: #7f8c8d;">2</div>
                        <div class="metric-label">场景数</div>
                    </div>
                </div>
            </div>

            <div class="summary-card overall">
                <div class="card-header">
                    <div class="card-icon">📊</div>
                    <div class="card-title">分析统计</div>
                </div>
                <div class="card-metrics">
                    <div class="metric">
                        <div class="metric-value" style="color: #9b59b6;">2</div>
                        <div class="metric-label">场景总数</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" style="color: #9b59b6;">3</div>
                        <div class="metric-label">目标类别</div>
                    </div>
                </div>
            </div>
        </div>
        
            </div>

            <div class="section">
                <h2>分类别概览</h2>
                
        <div class="summary-cards">
        
            <div class="summary-card good">
                <div class="card-header">
                    <div class="card-icon">🚗</div>
                    <div class="card-title">机动车检测</div>
                </div>
                <div class="card-metrics">
                    <div class="metric">
                        <div class="metric-value" style="color: #e67e22;">54.1%</div>
                        <div class="metric-label">检出率</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" style="color: #3498db;">73.8%</div>
                        <div class="metric-label">准确率</div>
                    </div>
                </div>
            </div>
            
            <div class="summary-card excellent">
                <div class="card-header">
                    <div class="card-icon">🚶</div>
                    <div class="card-title">行人检测</div>
                </div>
                <div class="card-metrics">
                    <div class="metric">
                        <div class="metric-value" style="color: #2ecc71;">88.5%</div>
                        <div class="metric-label">检出率</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" style="color: #2ecc71;">100.0%</div>
                        <div class="metric-label">准确率</div>
                    </div>
                </div>
            </div>
            
            <div class="summary-card excellent">
                <div class="card-header">
                    <div class="card-icon">🚲</div>
                    <div class="card-title">非机动车检测</div>
                </div>
                <div class="card-metrics">
                    <div class="metric">
                        <div class="metric-value" style="color: #2ecc71;">84.7%</div>
                        <div class="metric-label">检出率</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" style="color: #2ecc71;">89.3%</div>
                        <div class="metric-label">准确率</div>
                    </div>
                </div>
            </div>
            
        </div>
        
            </div>

            <div class="section">
                <h2>场景趋势分析</h2>
                
        <div id="master-trend-container" style="width: 100%; height: 700px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: #ffffff; margin-bottom: 15px;">
                    全场景趋势分析
                </h3>
                <div id="master-trend-buttons" style="margin-bottom: 20px;">
        
                    <button onclick="switchMasterTrend('0-50米', this)"
                            class="master-trend-btn master-trend-active"
                            style="margin: 0 10px; padding: 8px 16px;
                                   background: #2d2d2d;
                                   color: #ffffff;
                                   border: 1px solid #404040;
                                   border-radius: 4px; cursor: pointer;">
                        0-50米
                    </button>
            
                    <button onclick="switchMasterTrend('50-100米', this)"
                            class="master-trend-btn "
                            style="margin: 0 10px; padding: 8px 16px;
                                   background: #2d2d2d;
                                   color: #ffffff;
                                   border: 1px solid #404040;
                                   border-radius: 4px; cursor: pointer;">
                        50-100米
                    </button>
            
                    <button onclick="switchMasterTrend('总体', this)"
                            class="master-trend-btn "
                            style="margin: 0 10px; padding: 8px 16px;
                                   background: #2d2d2d;
                                   color: #ffffff;
                                   border: 1px solid #404040;
                                   border-radius: 4px; cursor: pointer;">
                        总体
                    </button>
            
                </div>
            </div>
            <div id="master-trend-chart" style="width: 100%; height: 600px;"></div>
        </div>

        <script>
            // 场景名称数据
            var masterScenes = ["deqing-001", "deqing-002"];

            // 趋势图数据
            var masterTrendData = {"0-50米": {"机动车-准确率": [73.8, 73.8], "机动车-检出率": [54.1, 54.1], "行人-准确率": [100.0, 100.0], "行人-检出率": [88.5, 88.5], "非机动车-准确率": [89.3, 89.3], "非机动车-检出率": [84.7, 84.7]}, "50-100米": {"机动车-准确率": [73.8, 73.8], "机动车-检出率": [54.1, 54.1], "行人-准确率": [100.0, 100.0], "行人-检出率": [88.5, 88.5], "非机动车-准确率": [89.3, 89.3], "非机动车-检出率": [84.7, 84.7]}, "总体": {"机动车-准确率": [73.8, 73.8], "机动车-检出率": [54.1, 54.1], "行人-准确率": [100.0, 100.0], "行人-检出率": [88.5, 88.5], "非机动车-准确率": [89.3, 89.3], "非机动车-检出率": [84.7, 84.7]}};

            // 颜色配置
            var masterCategoryColors = {
                '行人': ['#1f77b4', '#aec7e8'],
                '机动车': ['#2ca02c', '#98df8a'],
                '非机动车': ['#ff7f0e', '#ffbb78']
            };

            // 趋势图配置
            function getMasterTrendOption(distBin) {
                var seriesData = masterTrendData[distBin];
                var series = [];

                Object.keys(seriesData).forEach(function(seriesName) {
                    var category = seriesName.split('-')[0];
                    var metric = seriesName.split('-')[1];
                    var colorIndex = metric === '准确率' ? 0 : 1;
                    var lineType = metric === '准确率' ? 'solid' : 'dashed';

                    series.push({
                        name: seriesName,
                        type: 'line',
                        data: seriesData[seriesName],
                        smooth: false,
                        symbol: 'none',
                        lineStyle: {
                            color: masterCategoryColors[category][colorIndex],
                            width: 3,
                            type: lineType
                        },
                        emphasis: {
                            focus: 'series',
                            lineStyle: {
                                width: 4
                            }
                        }
                    });
                });

                return {
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    title: {
                        text: distBin + ' - 场景性能趋势',
                        left: 'center',
                        textStyle: {
                            color: '#5a6c7d',
                            fontSize: 16
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        borderColor: '#bdc3c7',
                        textStyle: {
                            color: '#2c3e50'
                        }
                    },
                    legend: {
                        top: '8%',
                        textStyle: {
                            color: '#5a6c7d'
                        }
                    },
                    grid: {
                        left: '10%',
                        right: '10%',
                        top: '20%',
                        bottom: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: masterScenes,
                        name: '场景',
                        axisLabel: {
                            color: '#7f8c8d',
                            rotate: 45
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#bdc3c7'
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '性能指标 (%)',
                        min: 0,
                        max: 100,
                        axisLabel: {
                            color: '#7f8c8d'
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#bdc3c7'
                            }
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#ecf0f1',
                                type: 'dashed'
                            }
                        }
                    },
                    series: series
                };
            }

            // 初始化趋势图
            var masterTrendChart = echarts.init(document.getElementById('master-trend-chart'));
            masterTrendChart.setOption(getMasterTrendOption('0-50米'));

            // 切换函数
            function switchMasterTrend(distBin, clickedButton) {
                // 更新按钮状态
                var buttons = document.querySelectorAll('#master-trend-buttons .master-trend-btn');
                buttons.forEach(btn => btn.classList.remove('master-trend-active'));
                clickedButton.classList.add('master-trend-active');

                // 更新趋势图
                masterTrendChart.setOption(getMasterTrendOption(distBin));
            }
        </script>

        <style>
            .master-trend-btn.master-trend-active {
                background: #00d4ff !important;
                color: #000000 !important;
            }
            .master-trend-btn:hover {
                background: #00d4ff !important;
                color: #000000 !important;
            }
        </style>
        
            </div>
        </body>
        </html>
        