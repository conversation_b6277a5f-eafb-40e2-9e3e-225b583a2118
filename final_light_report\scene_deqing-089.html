
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>deqing-089 - 性能分析报告</title>
                <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
                <style>
                    body {
                        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                        color: #1e293b;
                        font-family: Microsoft YaHei, 'Segoe UI', Arial, sans-serif;
                        margin: 0;
                        padding: 20px;
                        min-height: 100vh;
                    }
                    .section {
                        margin: 30px 0;
                        padding: 25px;
                        background: #ffffff;
                        border-radius: 16px;
                        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.1);
                        border: 1px solid #e2e8f0;
                        backdrop-filter: blur(10px);
                        transition: transform 0.2s ease, box-shadow 0.2s ease;
                    }
                    .section:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08);
                    }
                    .gauge-container {
                        display: flex;
                        justify-content: space-around;
                        align-items: center;
                        flex-wrap: wrap;
                    }
                    h1 {
                        text-align: center;
                        color: #1e293b;
                        font-weight: 600;
                        font-size: 2.5rem;
                        margin-bottom: 10px;
                        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        background-clip: text;
                    }
                    h2 {
                        text-align: center;
                        color: #64748b;
                        font-weight: 500;
                        font-size: 1.8rem;
                        margin-bottom: 20px;
                    }
                </style>
            </head>
            <body>
                <h1>deqing-089 - 性能分析报告</h1>

                <div class="section">
                    <h2>总体性能指标</h2>
                    
        <div id="gauge-container-deqing-089" style="width: 100%; height: 400px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: #2c3e50; margin-bottom: 15px;">
                    deqing-089 - 总体性能仪表盘
                </h3>
            </div>
            <div id="gauge-chart-deqing-089" style="width: 100%; height: 350px;"></div>
        </div>

        <script>
            // 初始化仪表盘
            var gaugeChart_deqing_089 = echarts.init(document.getElementById('gauge-chart-deqing-089'));

            var gaugeOption_deqing_089 = {
                backgroundColor: 'rgba(255, 255, 255, 0.5)',
                series: [
                    
            {
                name: '检出率',
                type: 'gauge',
                center: ['25%', '50%'],
                radius: '70%',
                min: 0,
                max: 100,
                splitNumber: 10,
                axisLine: {
                    lineStyle: {
                        color: [[0.3, '#e67e22'], [0.7, '#3498db'], [1, '#3498db']],
                        width: 8
                    }
                },
                pointer: {
                    itemStyle: { color: 'auto' }
                },
                axisTick: {
                    distance: -30,
                    length: 8,
                    lineStyle: { color: '#fff', width: 2 }
                },
                splitLine: {
                    distance: -30,
                    length: 30,
                    lineStyle: { color: '#fff', width: 4 }
                },
                axisLabel: {
                    color: 'auto',
                    distance: 40,
                    fontSize: 12
                },
                detail: {
                    valueAnimation: true,
                    formatter: '{value}%',
                    color: '#3498db',
                    fontSize: 16,
                    offsetCenter: [0, '70%']
                },
                title: {
                    offsetCenter: [0, '90%'],
                    fontSize: 14,
                    color: '#5a6c7d'
                },
                data: [{ value: 64.2, name: '检出率' }]
            }
            ,
                    
            {
                name: '准确率',
                type: 'gauge',
                center: ['75%', '50%'],
                radius: '70%',
                min: 0,
                max: 100,
                splitNumber: 10,
                axisLine: {
                    lineStyle: {
                        color: [[0.3, '#e67e22'], [0.7, '#f39c12'], [1, '#2ecc71']],
                        width: 8
                    }
                },
                pointer: {
                    itemStyle: { color: 'auto' }
                },
                axisTick: {
                    distance: -30,
                    length: 8,
                    lineStyle: { color: '#fff', width: 2 }
                },
                splitLine: {
                    distance: -30,
                    length: 30,
                    lineStyle: { color: '#fff', width: 4 }
                },
                axisLabel: {
                    color: 'auto',
                    distance: 40,
                    fontSize: 12
                },
                detail: {
                    valueAnimation: true,
                    formatter: '{value}%',
                    color: '#2ecc71',
                    fontSize: 16,
                    offsetCenter: [0, '70%']
                },
                title: {
                    offsetCenter: [0, '90%'],
                    fontSize: 14,
                    color: '#5a6c7d'
                },
                data: [{ value: 82.9, name: '准确率' }]
            }
            
                ]
            };

            gaugeChart_deqing_089.setOption(gaugeOption_deqing_089);
            window.addEventListener('resize', () => gaugeChart_deqing_089.resize());
        </script>
        
                </div>

                <div class="section">
                    <h2>距离段性能对比</h2>
                    
        <div id="bar-chart-deqing_089" style="width: 1000px; height: 500px; margin: 20px auto;"></div>

        <script>
            // 初始化柱状图
            var barChart_deqing_089 = echarts.init(document.getElementById('bar-chart-deqing_089'));

            var barOption_deqing_089 = {
  "backgroundColor": "#f8fafc",
  "title": {
    "text": "deqing-089 - 距离段性能对比",
    "subtext": "不同目标类别在各距离段的准确率和检出率对比",
    "textStyle": {
      "color": "#1e293b",
      "fontSize": 16
    },
    "subtextStyle": {
      "color": "#64748b",
      "fontSize": 12
    }
  },
  "tooltip": {
    "trigger": "axis",
    "backgroundColor": "rgba(45, 45, 45, 0.95)",
    "borderColor": "#e2e8f0",
    "textStyle": {
      "color": "#ffffff"
    }
  },
  "legend": {
    "top": "8%",
    "textStyle": {
      "color": "#1e293b"
    }
  },
  "xAxis": {
    "type": "category",
    "data": [
      "0-50米",
      "50-100米",
      "总体"
    ],
    "name": "距离段",
    "axisLabel": {
      "color": "#1e293b"
    },
    "axisLine": {
      "lineStyle": {
        "color": "#e2e8f0"
      }
    }
  },
  "yAxis": {
    "type": "value",
    "name": "性能指标 (%)",
    "min": 0,
    "max": 100,
    "axisLabel": {
      "color": "#1e293b"
    },
    "axisLine": {
      "lineStyle": {
        "color": "#e2e8f0"
      }
    },
    "splitLine": {
      "show": true,
      "lineStyle": {
        "color": "#e2e8f0",
        "type": "dashed"
      }
    }
  },
  "series": [
    {
      "name": "机动车-准确率",
      "type": "bar",
      "data": [
        86.1,
        77.1,
        82.0
      ],
      "itemStyle": {
        "color": "#10b981"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "机动车-检出率",
      "type": "bar",
      "data": [
        69.1,
        55.4,
        62.4
      ],
      "itemStyle": {
        "color": "#6ee7b7"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "行人-准确率",
      "type": "bar",
      "data": [
        96.0,
        85.9,
        91.1
      ],
      "itemStyle": {
        "color": "#3b82f6"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "行人-检出率",
      "type": "bar",
      "data": [
        76.9,
        67.3,
        72.2
      ],
      "itemStyle": {
        "color": "#93c5fd"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "非机动车-准确率",
      "type": "bar",
      "data": [
        81.2,
        69.0,
        75.0
      ],
      "itemStyle": {
        "color": "#f59e0b"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "非机动车-检出率",
      "type": "bar",
      "data": [
        64.0,
        51.9,
        57.7
      ],
      "itemStyle": {
        "color": "#fbbf24"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    }
  ]
};

            barChart_deqing_089.setOption(barOption_deqing_089);
            window.addEventListener('resize', () => barChart_deqing_089.resize());
        </script>
        
                </div>

                <div class="section">
                    <h2>雷达图分析</h2>
                    
        <style>
            .radar-btn:hover {
                background: #3b82f6 !important;
                color: white !important;
                border-color: #3b82f6 !important;
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(59,130,246,0.3) !important;
            }
            .radar-active {
                background: #3b82f6 !important;
                color: white !important;
                border-color: #3b82f6 !important;
                box-shadow: 0 4px 12px rgba(59,130,246,0.3) !important;
            }
        </style>
        <div id="radar-container-deqing-089" style="width: 100%; height: 600px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: #1e293b; margin-bottom: 15px;">
                    deqing-089 - 雷达图分析
                </h3>
                <div id="radar-buttons-deqing-089" style="margin-bottom: 20px;">
        
                    <button onclick="switchRadar_deqing_089('0-50米', this)"
                            class="radar-btn radar-active"
                            style="margin: 0 10px; padding: 10px 20px;
                                   background: #ffffff;
                                   color: #1e293b;
                                   border: 2px solid #e2e8f0;
                                   border-radius: 8px; cursor: pointer;
                                   font-weight: 500; transition: all 0.2s ease;
                                   box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        0-50米
                    </button>
            
                    <button onclick="switchRadar_deqing_089('50-100米', this)"
                            class="radar-btn "
                            style="margin: 0 10px; padding: 10px 20px;
                                   background: #ffffff;
                                   color: #1e293b;
                                   border: 2px solid #e2e8f0;
                                   border-radius: 8px; cursor: pointer;
                                   font-weight: 500; transition: all 0.2s ease;
                                   box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        50-100米
                    </button>
            
                    <button onclick="switchRadar_deqing_089('总体', this)"
                            class="radar-btn "
                            style="margin: 0 10px; padding: 10px 20px;
                                   background: #ffffff;
                                   color: #1e293b;
                                   border: 2px solid #e2e8f0;
                                   border-radius: 8px; cursor: pointer;
                                   font-weight: 500; transition: all 0.2s ease;
                                   box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        总体
                    </button>
            
                </div>
            </div>
            <div id="radar-chart-deqing-089" style="width: 100%; height: 500px;"></div>
        </div>

        <script>
            // 雷达图数据
            var radarData_deqing_089 = {"0-50米": [86.0724233983287, 69.12751677852349, 96.03399433427762, 76.87074829931973, 81.15015974440894, 63.97984886649874], "50-100米": [77.12418300653596, 55.39906103286385, 85.88588588588588, 67.29411764705883, 69.04024767801857, 51.86046511627907], "总体": [81.95488721804512, 62.42840778923253, 91.10787172011662, 72.17090069284065, 75.0, 57.67835550181378]};

            // 雷达图配置
            var radarOption_deqing_089 = {
                backgroundColor: '#f8fafc',
                radar: {
                    indicator: [
                        {name: '人-准确率', max: 100},
                        {name: '人-检出率', max: 100},
                        {name: '车-准确率', max: 100},
                        {name: '车-检出率', max: 100},
                        {name: '非-准确率', max: 100},
                        {name: '非-检出率', max: 100}
                    ],
                    center: ['50%', '50%'],
                    radius: '70%',
                    axisName: {
                        color: '#1e293b',
                        fontSize: 12
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#e2e8f0'
                        }
                    },
                    splitArea: {
                        show: false
                    }
                },
                series: [{
                    type: 'radar',
                    data: [{
                        value: radarData_deqing_089['0-50米'],
                        name: '0-50米',
                        areaStyle: {
                            color: 'rgba(0, 212, 255, 0.3)'
                        },
                        lineStyle: {
                            color: '#3b82f6'
                        }
                    }]
                }]
            };

            // 初始化雷达图
            var radarChart_deqing_089 = echarts.init(document.getElementById('radar-chart-deqing-089'));
            radarChart_deqing_089.setOption(radarOption_deqing_089);

            // 切换函数
            function switchRadar_deqing_089(distBin, clickedButton) {
                // 更新按钮状态
                var buttons = document.querySelectorAll('#radar-buttons-deqing-089 .radar-btn');
                buttons.forEach(btn => btn.classList.remove('radar-active'));
                clickedButton.classList.add('radar-active');

                // 更新雷达图数据
                radarOption_deqing_089.series[0].data[0].value = radarData_deqing_089[distBin];
                radarOption_deqing_089.series[0].data[0].name = distBin;
                radarChart_deqing_089.setOption(radarOption_deqing_089);
            }
        </script>

        <style>
            .radar-btn.radar-active {
                background: #3b82f6 !important;
                color: #000000 !important;
            }
            .radar-btn:hover {
                background: #3b82f6 !important;
                color: #000000 !important;
            }
        </style>
        
                </div>

                <div class="section">
                    <h2>时间趋势分析</h2>
                    
        <style>
            .trend-btn:hover {
                background: #3b82f6 !important;
                color: white !important;
                border-color: #3b82f6 !important;
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(59,130,246,0.3) !important;
            }
            .trend-active {
                background: #3b82f6 !important;
                color: white !important;
                border-color: #3b82f6 !important;
                box-shadow: 0 4px 12px rgba(59,130,246,0.3) !important;
            }
        </style>
        <div id="trend-container-deqing-089" style="width: 100%; height: 700px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: #1e293b; margin-bottom: 15px;">
                    deqing-089 - 时间趋势分析
                </h3>
                <div id="trend-buttons-deqing-089" style="margin-bottom: 20px;">
        
                    <button onclick="switchTrend_deqing_089('0-50米', this)"
                            class="trend-btn trend-active"
                            style="margin: 0 10px; padding: 10px 20px;
                                   background: #ffffff;
                                   color: #1e293b;
                                   border: 2px solid #e2e8f0;
                                   border-radius: 8px; cursor: pointer;
                                   font-weight: 500; transition: all 0.2s ease;
                                   box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        0-50米
                    </button>
            
                    <button onclick="switchTrend_deqing_089('50-100米', this)"
                            class="trend-btn "
                            style="margin: 0 10px; padding: 10px 20px;
                                   background: #ffffff;
                                   color: #1e293b;
                                   border: 2px solid #e2e8f0;
                                   border-radius: 8px; cursor: pointer;
                                   font-weight: 500; transition: all 0.2s ease;
                                   box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        50-100米
                    </button>
            
                    <button onclick="switchTrend_deqing_089('总体', this)"
                            class="trend-btn "
                            style="margin: 0 10px; padding: 10px 20px;
                                   background: #ffffff;
                                   color: #1e293b;
                                   border: 2px solid #e2e8f0;
                                   border-radius: 8px; cursor: pointer;
                                   font-weight: 500; transition: all 0.2s ease;
                                   box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        总体
                    </button>
            
                </div>
            </div>
            <div id="trend-chart-deqing-089" style="width: 100%; height: 600px;"></div>
        </div>

        <script>
            // 时间戳数据
            var timestamps_deqing_089 = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69];

            // 趋势图数据
            var trendData_deqing_089 = {"0-50米": {"机动车-准确率": [75.0, 87.5, 75.0, 83.3, 100.0, 100.0, 75.0, 100.0, 100.0, 75.0, 87.5, 100.0, 87.5, 100.0, 100.0, 75.0, 85.7, 83.3, 75.0, 100.0, 75.0, 100.0, 80.0, 71.4, 100.0, 88.9, 100.0, 100.0, 100.0, 71.4, 100.0, 75.0, 100.0, 100.0, 83.3, 83.3, 100.0, 100.0, 85.7, 75.0, 77.8, 100.0, 100.0, 80.0, 100.0, 80.0, 85.7, 100.0, 75.0, 100.0, 75.0, 71.4, 100.0, 72.7, 100.0, 100.0, 80.0, 85.7, 100.0, 66.7, 75.0, 100.0, 85.7, 100.0, 100.0, 100.0, 100.0, 66.7], "机动车-检出率": [66.7, 77.8, 50.0, 83.3, 77.8, 83.3, 75.0, 80.0, 33.3, 60.0, 77.8, 80.0, 70.0, 62.5, 71.4, 75.0, 66.7, 71.4, 60.0, 66.7, 75.0, 66.7, 80.0, 62.5, 80.0, 80.0, 77.8, 66.7, 66.7, 62.5, 60.0, 75.0, 80.0, 60.0, 83.3, 83.3, 60.0, 66.7, 66.7, 60.0, 77.8, 66.7, 66.7, 66.7, 50.0, 57.1, 66.7, 62.5, 75.0, 71.4, 60.0, 71.4, 60.0, 80.0, 50.0, 66.7, 66.7, 66.7, 66.7, 75.0, 60.0, 80.0, 75.0, 66.7, 50.0, 75.0, 50.0, 60.0], "行人-准确率": [100.0, 100.0, 100.0, 85.7, 100.0, 100.0, 88.9, 100.0, 85.7, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 85.7, 88.9, 100.0, 100.0, 100.0, 100.0, 88.9, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 87.5, 100.0, 87.5, 100.0, 100.0, 100.0, 100.0, 100.0, 87.5, 100.0, 88.9, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 85.7, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 83.3, 85.7, 100.0, 100.0, 87.5, 100.0, 100.0, 100.0], "行人-检出率": [66.7, 85.7, 66.7, 85.7, 77.8, 80.0, 80.0, 83.3, 75.0, 83.3, 70.0, 70.0, 66.7, 75.0, 80.0, 66.7, 71.4, 80.0, 75.0, 85.7, 80.0, 80.0, 75.0, 85.7, 66.7, 80.0, 60.0, 75.0, 77.8, 71.4, 60.0, 75.0, 77.8, 66.7, 70.0, 75.0, 60.0, 83.3, 77.8, 80.0, 77.8, 83.3, 80.0, 87.5, 75.0, 66.7, 75.0, 80.0, 80.0, 75.0, 77.8, 66.7, 80.0, 83.3, 85.7, 80.0, 71.4, 75.0, 87.5, 88.9, 83.3, 75.0, 75.0, 75.0, 77.8, 77.8, 60.0, 66.7], "非机动车-准确率": [100.0, 100.0, 100.0, 75.0, 83.3, 75.0, 77.8, 100.0, 66.7, 80.0, 100.0, 71.4, 100.0, 75.0, 100.0, 100.0, 100.0, 66.7, 100.0, 66.7, 100.0, 100.0, 80.0, 66.7, 75.0, 83.3, 100.0, 75.0, 85.7, 100.0, 66.7, 75.0, 71.4, 83.3, 100.0, 100.0, 100.0, 100.0, 80.0, 100.0, 66.7, 100.0, 100.0, 100.0, 66.7, 100.0, 100.0, 100.0, 66.7, 72.7, 100.0, 100.0, 63.6, 100.0, 75.0, 100.0, 62.5, 87.5, 75.0, 100.0, 70.0, 100.0, 66.7, 75.0, 80.0, 100.0, 75.0, 100.0], "非机动车-检出率": [50.0, 70.0, 66.7, 50.0, 62.5, 75.0, 70.0, 50.0, 60.0, 57.1, 50.0, 83.3, 66.7, 50.0, 60.0, 60.0, 66.7, 50.0, 80.0, 66.7, 50.0, 71.4, 80.0, 66.7, 75.0, 62.5, 66.7, 60.0, 75.0, 40.0, 80.0, 75.0, 62.5, 71.4, 66.7, 33.3, 75.0, 66.7, 80.0, 33.3, 50.0, 71.4, 40.0, 60.0, 66.7, 50.0, 40.0, 33.3, 60.0, 80.0, 50.0, 66.7, 77.8, 50.0, 75.0, 50.0, 71.4, 70.0, 66.7, 66.7, 70.0, 50.0, 80.0, 50.0, 66.7, 75.0, 50.0, 66.7]}, "50-100米": {"机动车-准确率": [66.7, 100.0, 66.7, 75.0, 55.6, 80.0, 75.0, 100.0, 75.0, 75.0, 100.0, 75.0, 100.0, 80.0, 100.0, 75.0, 75.0, 62.5, 100.0, 60.0, 100.0, 83.3, 80.0, 100.0, 66.7, 66.7, 100.0, 75.0, 100.0, 70.0, 100.0, 66.7, 100.0, 100.0, 80.0, 100.0, 80.0, 100.0, 75.0, 77.8, 75.0, 100.0, 100.0, 53.8, 100.0, 100.0, 66.7, 66.7, 60.0, 60.0, 100.0, 100.0, 100.0, 83.3, 77.8, 100.0, 80.0, 85.7, 85.7, 77.8, 100.0, 60.0, 80.0, 75.0, 100.0, 100.0, 60.0, 60.0], "机动车-检出率": [50.0, 50.0, 40.0, 50.0, 50.0, 44.4, 75.0, 66.7, 50.0, 50.0, 71.4, 50.0, 66.7, 44.4, 33.3, 42.9, 60.0, 62.5, 66.7, 60.0, 66.7, 55.6, 57.1, 40.0, 66.7, 44.4, 25.0, 60.0, 66.7, 77.8, 33.3, 50.0, 33.3, 60.0, 50.0, 33.3, 80.0, 50.0, 60.0, 77.8, 42.9, 42.9, 80.0, 70.0, 25.0, 33.3, 40.0, 40.0, 50.0, 75.0, 40.0, 25.0, 75.0, 62.5, 77.8, 60.0, 44.4, 75.0, 60.0, 70.0, 33.3, 50.0, 57.1, 50.0, 42.9, 33.3, 60.0, 60.0], "行人-准确率": [85.7, 100.0, 100.0, 85.7, 83.3, 85.7, 71.4, 77.8, 80.0, 100.0, 85.7, 71.4, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 66.7, 100.0, 87.5, 100.0, 85.7, 85.7, 100.0, 100.0, 100.0, 100.0, 85.7, 100.0, 85.7, 100.0, 75.0, 100.0, 100.0, 100.0, 80.0, 100.0, 100.0, 100.0, 75.0, 75.0, 75.0, 100.0, 100.0, 100.0, 100.0, 80.0, 70.0, 100.0, 80.0, 80.0, 100.0, 66.7, 80.0, 100.0, 85.7, 100.0, 71.4, 66.7, 100.0, 87.5, 100.0, 100.0, 100.0, 66.7, 85.7, 72.7], "行人-检出率": [75.0, 50.0, 62.5, 60.0, 62.5, 60.0, 71.4, 70.0, 66.7, 66.7, 75.0, 71.4, 75.0, 80.0, 75.0, 60.0, 85.7, 50.0, 66.7, 66.7, 77.8, 50.0, 60.0, 75.0, 33.3, 33.3, 66.7, 33.3, 75.0, 80.0, 66.7, 75.0, 75.0, 57.1, 66.7, 60.0, 50.0, 66.7, 33.3, 33.3, 60.0, 85.7, 75.0, 50.0, 80.0, 66.7, 75.0, 80.0, 77.8, 71.4, 66.7, 57.1, 75.0, 57.1, 88.9, 50.0, 85.7, 50.0, 71.4, 50.0, 70.0, 77.8, 50.0, 83.3, 60.0, 80.0, 66.7, 80.0], "非机动车-准确率": [71.4, 71.4, 50.0, 100.0, 100.0, 100.0, 50.0, 66.7, 100.0, 62.5, 100.0, 50.0, 50.0, 75.0, 70.0, 50.0, 83.3, 100.0, 83.3, 50.0, 66.7, 100.0, 66.7, 58.3, 75.0, 66.7, 66.7, 75.0, 60.0, 100.0, 57.1, 75.0, 100.0, 100.0, 100.0, 66.7, 80.0, 66.7, 80.0, 75.0, 100.0, 50.0, 71.4, 100.0, 80.0, 66.7, 55.6, 66.7, 75.0, 100.0, 66.7, 100.0, 66.7, 50.0, 80.0, 60.0, 100.0, 60.0, 77.8, 100.0, 100.0, 50.0, 100.0, 66.7, 50.0, 75.0, 85.7, 60.0], "非机动车-检出率": [62.5, 55.6, 50.0, 33.3, 25.0, 33.3, 66.7, 75.0, 33.3, 71.4, 60.0, 33.3, 50.0, 37.5, 70.0, 25.0, 50.0, 25.0, 55.6, 50.0, 33.3, 40.0, 66.7, 70.0, 37.5, 66.7, 40.0, 60.0, 42.9, 50.0, 66.7, 75.0, 33.3, 33.3, 33.3, 66.7, 57.1, 44.4, 44.4, 50.0, 50.0, 60.0, 50.0, 40.0, 50.0, 57.1, 62.5, 40.0, 60.0, 50.0, 40.0, 42.9, 33.3, 66.7, 57.1, 60.0, 60.0, 37.5, 70.0, 66.7, 40.0, 50.0, 50.0, 44.4, 33.3, 60.0, 75.0, 66.7]}, "总体": {"机动车-准确率": [70.8, 93.8, 70.8, 79.2, 77.8, 90.0, 75.0, 100.0, 87.5, 75.0, 93.8, 87.5, 93.8, 90.0, 100.0, 75.0, 80.4, 72.9, 87.5, 80.0, 87.5, 91.7, 80.0, 85.7, 83.3, 77.8, 100.0, 87.5, 100.0, 70.7, 100.0, 70.8, 100.0, 100.0, 81.7, 91.7, 90.0, 100.0, 80.4, 76.4, 76.4, 100.0, 100.0, 66.9, 100.0, 90.0, 76.2, 83.3, 67.5, 80.0, 87.5, 85.7, 100.0, 78.0, 88.9, 100.0, 80.0, 85.7, 92.9, 72.2, 87.5, 80.0, 82.9, 87.5, 100.0, 100.0, 80.0, 63.3], "机动车-检出率": [58.3, 63.9, 45.0, 66.7, 63.9, 63.9, 75.0, 73.3, 41.7, 55.0, 74.6, 65.0, 68.3, 53.5, 52.4, 58.9, 63.3, 67.0, 63.3, 63.3, 70.8, 61.1, 68.6, 51.2, 73.3, 62.2, 51.4, 63.3, 66.7, 70.1, 46.7, 62.5, 56.7, 60.0, 66.7, 58.3, 70.0, 58.3, 63.3, 68.9, 60.3, 54.8, 73.3, 68.3, 37.5, 45.2, 53.3, 51.2, 62.5, 73.2, 50.0, 48.2, 67.5, 71.2, 63.9, 63.3, 55.6, 70.8, 63.3, 72.5, 46.7, 65.0, 66.1, 58.3, 46.4, 54.2, 55.0, 60.0], "行人-准确率": [92.9, 100.0, 100.0, 85.7, 91.7, 92.9, 80.2, 88.9, 82.9, 100.0, 92.9, 85.7, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 83.3, 92.9, 88.2, 100.0, 92.9, 92.9, 100.0, 94.4, 100.0, 100.0, 92.9, 100.0, 92.9, 100.0, 81.2, 100.0, 93.8, 100.0, 90.0, 100.0, 100.0, 100.0, 81.2, 87.5, 81.9, 100.0, 100.0, 100.0, 100.0, 90.0, 85.0, 92.9, 90.0, 90.0, 100.0, 83.3, 90.0, 100.0, 92.9, 100.0, 85.7, 83.3, 91.7, 86.6, 100.0, 100.0, 93.8, 83.3, 92.9, 86.4], "行人-检出率": [70.8, 67.9, 64.6, 72.9, 70.1, 70.0, 75.7, 76.7, 70.8, 75.0, 72.5, 70.7, 70.8, 77.5, 77.5, 63.3, 78.6, 65.0, 70.8, 76.2, 78.9, 65.0, 67.5, 80.4, 50.0, 56.7, 63.3, 54.2, 76.4, 75.7, 63.3, 75.0, 76.4, 61.9, 68.3, 67.5, 55.0, 75.0, 55.6, 56.7, 68.9, 84.5, 77.5, 68.8, 77.5, 66.7, 75.0, 80.0, 78.9, 73.2, 72.2, 61.9, 77.5, 70.2, 87.3, 65.0, 78.6, 62.5, 79.5, 69.4, 76.7, 76.4, 62.5, 79.2, 68.9, 78.9, 63.3, 73.3], "非机动车-准确率": [85.7, 85.7, 75.0, 87.5, 91.7, 87.5, 63.9, 83.3, 83.3, 71.2, 100.0, 60.7, 75.0, 75.0, 85.0, 75.0, 91.7, 83.3, 91.7, 58.3, 83.3, 100.0, 73.3, 62.5, 75.0, 75.0, 83.3, 75.0, 72.9, 100.0, 61.9, 75.0, 85.7, 91.7, 100.0, 83.3, 90.0, 83.3, 80.0, 87.5, 83.3, 75.0, 85.7, 100.0, 73.3, 83.3, 77.8, 83.3, 70.8, 86.4, 83.3, 100.0, 65.2, 75.0, 77.5, 80.0, 81.2, 73.8, 76.4, 100.0, 85.0, 75.0, 83.3, 70.8, 65.0, 87.5, 80.4, 80.0], "非机动车-检出率": [56.2, 62.8, 58.3, 41.7, 43.8, 54.2, 68.3, 62.5, 46.7, 64.3, 55.0, 58.3, 58.3, 43.8, 65.0, 42.5, 58.3, 37.5, 67.8, 58.3, 41.7, 55.7, 73.3, 68.3, 56.2, 64.6, 53.3, 60.0, 58.9, 45.0, 73.3, 75.0, 47.9, 52.4, 50.0, 50.0, 66.1, 55.6, 62.2, 41.7, 50.0, 65.7, 45.0, 50.0, 58.3, 53.6, 51.2, 36.7, 60.0, 65.0, 45.0, 54.8, 55.6, 58.3, 66.1, 55.0, 65.7, 53.8, 68.3, 66.7, 55.0, 50.0, 65.0, 47.2, 50.0, 67.5, 62.5, 66.7]}};

            // 颜色配置
            var categoryColors_deqing_089 = {
                '行人': ['#3b82f6', '#93c5fd'],
                '机动车': ['#10b981', '#6ee7b7'],
                '非机动车': ['#f59e0b', '#fbbf24']
            };

            // 趋势图配置
            function getTrendOption_deqing_089(distBin) {
                var seriesData = trendData_deqing_089[distBin];
                var series = [];

                Object.keys(seriesData).forEach(function(seriesName) {
                    var category = seriesName.split('-')[0];
                    var metric = seriesName.split('-')[1];
                    var colorIndex = metric === '准确率' ? 0 : 1;
                    var lineType = metric === '准确率' ? 'solid' : 'dashed';

                    series.push({
                        name: seriesName,
                        type: 'line',
                        data: seriesData[seriesName],
                        smooth: false,
                        symbol: 'none',
                        lineStyle: {
                            color: categoryColors_deqing_089[category][colorIndex],
                            width: 3,
                            type: lineType
                        },
                        emphasis: {
                            focus: 'series',
                            lineStyle: {
                                width: 4
                            }
                        }
                    });
                });

                return {
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    title: {
                        text: distBin + ' - 性能趋势',
                        left: 'center',
                        textStyle: {
                            color: '#5a6c7d',
                            fontSize: 16
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        borderColor: '#bdc3c7',
                        textStyle: {
                            color: '#2c3e50'
                        }
                    },
                    legend: {
                        top: '8%',
                        textStyle: {
                            color: '#5a6c7d'
                        }
                    },
                    grid: {
                        left: '10%',
                        right: '10%',
                        top: '20%',
                        bottom: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: timestamps_deqing_089,
                        name: '帧号',
                        axisLabel: {
                            color: '#7f8c8d'
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#bdc3c7'
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '性能指标 (%)',
                        min: 0,
                        max: 100,
                        axisLabel: {
                            color: '#7f8c8d'
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#bdc3c7'
                            }
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#ecf0f1',
                                type: 'dashed'
                            }
                        }
                    },
                    series: series
                };
            }

            // 初始化趋势图
            var trendChart_deqing_089 = echarts.init(document.getElementById('trend-chart-deqing-089'));
            trendChart_deqing_089.setOption(getTrendOption_deqing_089('0-50米'));

            // 切换函数
            function switchTrend_deqing_089(distBin, clickedButton) {
                // 更新按钮状态
                var buttons = document.querySelectorAll('#trend-buttons-deqing-089 .trend-btn');
                buttons.forEach(btn => btn.classList.remove('trend-active'));
                clickedButton.classList.add('trend-active');

                // 更新趋势图
                trendChart_deqing_089.setOption(getTrendOption_deqing_089(distBin));
            }
        </script>

        <style>
            .trend-btn.trend-active {
                background: #3b82f6 !important;
                color: #000000 !important;
            }
            .trend-btn:hover {
                background: #3b82f6 !important;
                color: #000000 !important;
            }
        </style>
        
                </div>
            </body>
            </html>
            