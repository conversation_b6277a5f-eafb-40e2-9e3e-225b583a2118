#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成50组场景的测试数据
"""

import pandas as pd
import numpy as np
import os
from openpyxl import Workbook
from openpyxl.utils.dataframe import dataframe_to_rows
import random

def generate_scene_data(scene_id: str, num_frames: int = 70) -> dict:
    """
    为单个场景生成测试数据，模拟原始Excel格式

    Args:
        scene_id: 场景ID (如 'scene-001')
        num_frames: 帧数

    Returns:
        包含工作表数据的字典
    """

    scene_data = {}

    # 生成总结工作表
    summary_data = [
        [None, '序号', '指标'],
        ['车', '检出率', round(random.uniform(0.5, 0.9), 6)],
        [None, '准确率', round(random.uniform(0.7, 0.95), 6)],
        ['非机动', '检出率', round(random.uniform(0.6, 0.9), 6)],
        [None, '准确率', round(random.uniform(0.7, 0.95), 6)],
        ['行人', '检出率', round(random.uniform(0.7, 0.95), 6)],
        [None, '准确率', round(random.uniform(0.8, 0.98), 6)]
    ]

    summary_df = pd.DataFrame(summary_data)
    summary_df.columns = ['Unnamed: 0', 'Unnamed: 1', 'Unnamed: 2']
    scene_data['总结'] = summary_df

    # 生成距离段工作表
    distance_ranges = ['0-50米', '50-100米']

    for dist_range in distance_ranges:
        # 创建表头
        columns = [
            'Unnamed: 0', '车', 'Unnamed: 2', 'Unnamed: 3', 'Unnamed: 4', 'Unnamed: 5',
            '非机动车', 'Unnamed: 7', 'Unnamed: 8', 'Unnamed: 9', 'Unnamed: 10',
            '行人', 'Unnamed: 12', 'Unnamed: 13', 'Unnamed: 14', 'Unnamed: 15',
            '总', 'Unnamed: 17'
        ]

        # 创建数据行
        data_rows = []

        # 第一行是表头
        header_row = ['序号', '真实数', '检出数', '误检数', '检出率', '准确率',
                     '真实数', '检出数', '误检数', '检出率', '准确率',
                     '真实数', '检出数', '误检数', '检出率', '准确率',
                     '单帧检出率', '单帧准确率']
        data_rows.append(header_row)

        # 生成数据行
        for frame_id in range(1, num_frames + 1):
            row = [frame_id]  # 序号

            # 为每个类别生成数据：车、非机动车、行人
            for category in ['车', '非机动车', '行人']:
                # 根据距离段和类别调整性能参数
                if dist_range == '0-50米':
                    if category == '行人':
                        base_recall = 0.85 + random.uniform(-0.1, 0.1)
                        base_precision = 0.90 + random.uniform(-0.1, 0.1)
                    elif category == '车':
                        base_recall = 0.75 + random.uniform(-0.15, 0.15)
                        base_precision = 0.80 + random.uniform(-0.15, 0.15)
                    else:  # 非机动车
                        base_recall = 0.70 + random.uniform(-0.15, 0.15)
                        base_precision = 0.75 + random.uniform(-0.15, 0.15)
                else:  # 50-100米
                    if category == '行人':
                        base_recall = 0.75 + random.uniform(-0.15, 0.15)
                        base_precision = 0.80 + random.uniform(-0.15, 0.15)
                    elif category == '车':
                        base_recall = 0.65 + random.uniform(-0.2, 0.2)
                        base_precision = 0.70 + random.uniform(-0.2, 0.2)
                    else:  # 非机动车
                        base_recall = 0.60 + random.uniform(-0.2, 0.2)
                        base_precision = 0.65 + random.uniform(-0.2, 0.2)

                # 确保值在合理范围内
                base_recall = max(0.1, min(0.95, base_recall))
                base_precision = max(0.1, min(0.95, base_precision))

                # 生成基础数据
                true_count = random.randint(3, 10)
                detected_count = int(true_count * base_recall)
                false_count = max(0, int(detected_count / base_precision) - detected_count) if base_precision > 0 else random.randint(0, 3)

                # 计算实际的检出率和准确率
                actual_recall = detected_count / true_count if true_count > 0 else 0
                actual_precision = detected_count / (detected_count + false_count) if (detected_count + false_count) > 0 else 1

                # 添加到行数据
                row.extend([
                    true_count,
                    detected_count,
                    false_count,
                    actual_recall,
                    actual_precision
                ])

            # 添加总计列（暂时用NaN填充）
            row.extend([None, None])

            data_rows.append(row)

        # 创建DataFrame
        df = pd.DataFrame(data_rows[1:], columns=columns)  # 跳过表头行
        df.iloc[0] = data_rows[0]  # 设置第一行为表头

        scene_data[dist_range] = df

    return scene_data

def create_excel_file(scene_id: str, scene_data: dict, output_dir: str):
    """
    创建Excel文件
    
    Args:
        scene_id: 场景ID
        scene_data: 场景数据字典
        output_dir: 输出目录
    """
    
    filename = os.path.join(output_dir, f"{scene_id}.xlsx")
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        for sheet_name, df in scene_data.items():
            df.to_excel(writer, sheet_name=sheet_name, index=False)
    
    print(f"已生成: {filename}")

def generate_all_test_data(num_scenes: int = 50, output_dir: str = "test_data"):
    """
    生成所有测试数据
    
    Args:
        num_scenes: 场景数量
        output_dir: 输出目录
    """
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"开始生成 {num_scenes} 个场景的测试数据...")
    
    for i in range(1, num_scenes + 1):
        scene_id = f"deqing-{i:03d}"  # scene-001, scene-002, ...
        
        # 生成场景数据
        scene_data = generate_scene_data(scene_id, num_frames=random.randint(60, 80))
        
        # 创建Excel文件
        create_excel_file(scene_id, scene_data, output_dir)
        
        if i % 10 == 0:
            print(f"已完成 {i}/{num_scenes} 个场景")
    
    print(f"\n所有测试数据生成完成！")
    print(f"输出目录: {output_dir}")
    print(f"共生成 {num_scenes} 个Excel文件")

if __name__ == "__main__":
    # 设置随机种子以获得可重现的结果
    random.seed(42)
    np.random.seed(42)
    
    # 生成50个场景的测试数据
    generate_all_test_data(num_scenes=100, output_dir="test_data_100_scenes")
