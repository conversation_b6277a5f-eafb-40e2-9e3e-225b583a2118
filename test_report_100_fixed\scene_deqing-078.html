
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>deqing-078 - 性能分析报告</title>
                <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
                <style>
                    body {
                        background: #1e1e1e;
                        color: #ffffff;
                        font-family: Microsoft YaHei, 'Segoe UI', Arial, sans-serif;
                        margin: 0;
                        padding: 20px;
                        min-height: 100vh;
                    }
                    .section {
                        margin: 30px 0;
                        padding: 25px;
                        background: #2d2d2d;
                        border-radius: 12px;
                        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                        border: 1px solid #404040;
                    }
                    .gauge-container {
                        display: flex;
                        justify-content: space-around;
                        align-items: center;
                        flex-wrap: wrap;
                    }
                    h1 {
                        text-align: center;
                        color: #ffffff;
                        font-weight: 300;
                        font-size: 2.5rem;
                        margin-bottom: 10px;
                        text-shadow: 0 2px 4px rgba(0,0,0,0.5);
                    }
                    h2 {
                        text-align: center;
                        color: #cccccc;
                        font-weight: 400;
                        font-size: 1.8rem;
                        margin-bottom: 20px;
                    }
                </style>
            </head>
            <body>
                <h1>deqing-078 - 性能分析报告</h1>

                <div class="section">
                    <h2>总体性能指标</h2>
                    
        <div id="gauge-container-deqing-078" style="width: 100%; height: 400px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: #2c3e50; margin-bottom: 15px;">
                    deqing-078 - 总体性能仪表盘
                </h3>
            </div>
            <div id="gauge-chart-deqing-078" style="width: 100%; height: 350px;"></div>
        </div>

        <script>
            // 初始化仪表盘
            var gaugeChart_deqing_078 = echarts.init(document.getElementById('gauge-chart-deqing-078'));

            var gaugeOption_deqing_078 = {
                backgroundColor: 'rgba(255, 255, 255, 0.5)',
                series: [
                    
            {
                name: '检出率',
                type: 'gauge',
                center: ['25%', '50%'],
                radius: '70%',
                min: 0,
                max: 100,
                splitNumber: 10,
                axisLine: {
                    lineStyle: {
                        color: [[0.3, '#e67e22'], [0.7, '#3498db'], [1, '#3498db']],
                        width: 8
                    }
                },
                pointer: {
                    itemStyle: { color: 'auto' }
                },
                axisTick: {
                    distance: -30,
                    length: 8,
                    lineStyle: { color: '#fff', width: 2 }
                },
                splitLine: {
                    distance: -30,
                    length: 30,
                    lineStyle: { color: '#fff', width: 4 }
                },
                axisLabel: {
                    color: 'auto',
                    distance: 40,
                    fontSize: 12
                },
                detail: {
                    valueAnimation: true,
                    formatter: '{value}%',
                    color: '#3498db',
                    fontSize: 16,
                    offsetCenter: [0, '70%']
                },
                title: {
                    offsetCenter: [0, '90%'],
                    fontSize: 14,
                    color: '#5a6c7d'
                },
                data: [{ value: 64.5, name: '检出率' }]
            }
            ,
                    
            {
                name: '准确率',
                type: 'gauge',
                center: ['75%', '50%'],
                radius: '70%',
                min: 0,
                max: 100,
                splitNumber: 10,
                axisLine: {
                    lineStyle: {
                        color: [[0.3, '#e67e22'], [0.7, '#f39c12'], [1, '#2ecc71']],
                        width: 8
                    }
                },
                pointer: {
                    itemStyle: { color: 'auto' }
                },
                axisTick: {
                    distance: -30,
                    length: 8,
                    lineStyle: { color: '#fff', width: 2 }
                },
                splitLine: {
                    distance: -30,
                    length: 30,
                    lineStyle: { color: '#fff', width: 4 }
                },
                axisLabel: {
                    color: 'auto',
                    distance: 40,
                    fontSize: 12
                },
                detail: {
                    valueAnimation: true,
                    formatter: '{value}%',
                    color: '#2ecc71',
                    fontSize: 16,
                    offsetCenter: [0, '70%']
                },
                title: {
                    offsetCenter: [0, '90%'],
                    fontSize: 14,
                    color: '#5a6c7d'
                },
                data: [{ value: 82.6, name: '准确率' }]
            }
            
                ]
            };

            gaugeChart_deqing_078.setOption(gaugeOption_deqing_078);
            window.addEventListener('resize', () => gaugeChart_deqing_078.resize());
        </script>
        
                </div>

                <div class="section">
                    <h2>距离段性能对比</h2>
                    
        <div id="bar-chart-deqing_078" style="width: 1000px; height: 500px; margin: 20px auto;"></div>

        <script>
            // 初始化柱状图
            var barChart_deqing_078 = echarts.init(document.getElementById('bar-chart-deqing_078'));

            var barOption_deqing_078 = {
  "backgroundColor": "#1e1e1e",
  "title": {
    "text": "deqing-078 - 距离段性能对比",
    "subtext": "不同目标类别在各距离段的准确率和检出率对比",
    "textStyle": {
      "color": "#ffffff",
      "fontSize": 16
    },
    "subtextStyle": {
      "color": "#cccccc",
      "fontSize": 12
    }
  },
  "tooltip": {
    "trigger": "axis",
    "backgroundColor": "rgba(45, 45, 45, 0.95)",
    "borderColor": "#404040",
    "textStyle": {
      "color": "#ffffff"
    }
  },
  "legend": {
    "top": "8%",
    "textStyle": {
      "color": "#ffffff"
    }
  },
  "xAxis": {
    "type": "category",
    "data": [
      "0-50米",
      "50-100米",
      "总体"
    ],
    "name": "距离段",
    "axisLabel": {
      "color": "#ffffff"
    },
    "axisLine": {
      "lineStyle": {
        "color": "#404040"
      }
    }
  },
  "yAxis": {
    "type": "value",
    "name": "性能指标 (%)",
    "min": 0,
    "max": 100,
    "axisLabel": {
      "color": "#ffffff"
    },
    "axisLine": {
      "lineStyle": {
        "color": "#404040"
      }
    },
    "splitLine": {
      "show": true,
      "lineStyle": {
        "color": "#404040",
        "type": "dashed"
      }
    }
  },
  "series": [
    {
      "name": "机动车-准确率",
      "type": "bar",
      "data": [
        87.2,
        77.2,
        82.5
      ],
      "itemStyle": {
        "color": "#2ca02c"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "机动车-检出率",
      "type": "bar",
      "data": [
        67.2,
        56.1,
        61.8
      ],
      "itemStyle": {
        "color": "#98df8a"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "行人-准确率",
      "type": "bar",
      "data": [
        96.5,
        85.1,
        90.5
      ],
      "itemStyle": {
        "color": "#1f77b4"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "行人-检出率",
      "type": "bar",
      "data": [
        77.3,
        70.1,
        73.5
      ],
      "itemStyle": {
        "color": "#aec7e8"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "非机动车-准确率",
      "type": "bar",
      "data": [
        81.6,
        68.4,
        74.9
      ],
      "itemStyle": {
        "color": "#ff7f0e"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "非机动车-检出率",
      "type": "bar",
      "data": [
        64.1,
        52.7,
        58.3
      ],
      "itemStyle": {
        "color": "#ffbb78"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    }
  ]
};

            barChart_deqing_078.setOption(barOption_deqing_078);
            window.addEventListener('resize', () => barChart_deqing_078.resize());
        </script>
        
                </div>

                <div class="section">
                    <h2>雷达图分析</h2>
                    
        <div id="radar-container-deqing-078" style="width: 100%; height: 600px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: #ffffff; margin-bottom: 15px;">
                    deqing-078 - 雷达图分析
                </h3>
                <div id="radar-buttons-deqing-078" style="margin-bottom: 20px;">
        
                    <button onclick="switchRadar_deqing_078('0-50米', this)"
                            class="radar-btn radar-active"
                            style="margin: 0 10px; padding: 8px 16px;
                                   background: #2d2d2d;
                                   color: #ffffff;
                                   border: 1px solid #404040;
                                   border-radius: 4px; cursor: pointer;">
                        0-50米
                    </button>
            
                    <button onclick="switchRadar_deqing_078('50-100米', this)"
                            class="radar-btn "
                            style="margin: 0 10px; padding: 8px 16px;
                                   background: #2d2d2d;
                                   color: #ffffff;
                                   border: 1px solid #404040;
                                   border-radius: 4px; cursor: pointer;">
                        50-100米
                    </button>
            
                    <button onclick="switchRadar_deqing_078('总体', this)"
                            class="radar-btn "
                            style="margin: 0 10px; padding: 8px 16px;
                                   background: #2d2d2d;
                                   color: #ffffff;
                                   border: 1px solid #404040;
                                   border-radius: 4px; cursor: pointer;">
                        总体
                    </button>
            
                </div>
            </div>
            <div id="radar-chart-deqing-078" style="width: 100%; height: 500px;"></div>
        </div>

        <script>
            // 雷达图数据
            var radarData_deqing_078 = {"0-50米": [87.1875, 67.2289156626506, 96.45161290322581, 77.2609819121447, 81.59509202453987, 64.09638554216868], "50-100米": [77.22419928825623, 56.07235142118863, 85.14285714285714, 70.11764705882354, 68.37349397590361, 52.66821345707656], "总体": [82.52911813643927, 61.84538653366584, 90.45454545454545, 73.52216748768473, 74.92401215805471, 58.274231678487]};

            // 雷达图配置
            var radarOption_deqing_078 = {
                backgroundColor: '#1e1e1e',
                radar: {
                    indicator: [
                        {name: '人-准确率', max: 100},
                        {name: '人-检出率', max: 100},
                        {name: '车-准确率', max: 100},
                        {name: '车-检出率', max: 100},
                        {name: '非-准确率', max: 100},
                        {name: '非-检出率', max: 100}
                    ],
                    center: ['50%', '50%'],
                    radius: '70%',
                    axisName: {
                        color: '#ffffff',
                        fontSize: 12
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#404040'
                        }
                    },
                    splitArea: {
                        show: false
                    }
                },
                series: [{
                    type: 'radar',
                    data: [{
                        value: radarData_deqing_078['0-50米'],
                        name: '0-50米',
                        areaStyle: {
                            color: 'rgba(0, 212, 255, 0.3)'
                        },
                        lineStyle: {
                            color: '#00d4ff'
                        }
                    }]
                }]
            };

            // 初始化雷达图
            var radarChart_deqing_078 = echarts.init(document.getElementById('radar-chart-deqing-078'));
            radarChart_deqing_078.setOption(radarOption_deqing_078);

            // 切换函数
            function switchRadar_deqing_078(distBin, clickedButton) {
                // 更新按钮状态
                var buttons = document.querySelectorAll('#radar-buttons-deqing-078 .radar-btn');
                buttons.forEach(btn => btn.classList.remove('radar-active'));
                clickedButton.classList.add('radar-active');

                // 更新雷达图数据
                radarOption_deqing_078.series[0].data[0].value = radarData_deqing_078[distBin];
                radarOption_deqing_078.series[0].data[0].name = distBin;
                radarChart_deqing_078.setOption(radarOption_deqing_078);
            }
        </script>

        <style>
            .radar-btn.radar-active {
                background: #00d4ff !important;
                color: #000000 !important;
            }
            .radar-btn:hover {
                background: #00d4ff !important;
                color: #000000 !important;
            }
        </style>
        
                </div>

                <div class="section">
                    <h2>时间趋势分析</h2>
                    
        <div id="trend-container-deqing-078" style="width: 100%; height: 700px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: #ffffff; margin-bottom: 15px;">
                    deqing-078 - 时间趋势分析
                </h3>
                <div id="trend-buttons-deqing-078" style="margin-bottom: 20px;">
        
                    <button onclick="switchTrend_deqing_078('0-50米', this)"
                            class="trend-btn trend-active"
                            style="margin: 0 10px; padding: 8px 16px;
                                   background: #2d2d2d;
                                   color: #ffffff;
                                   border: 1px solid #404040;
                                   border-radius: 4px; cursor: pointer;">
                        0-50米
                    </button>
            
                    <button onclick="switchTrend_deqing_078('50-100米', this)"
                            class="trend-btn "
                            style="margin: 0 10px; padding: 8px 16px;
                                   background: #2d2d2d;
                                   color: #ffffff;
                                   border: 1px solid #404040;
                                   border-radius: 4px; cursor: pointer;">
                        50-100米
                    </button>
            
                    <button onclick="switchTrend_deqing_078('总体', this)"
                            class="trend-btn "
                            style="margin: 0 10px; padding: 8px 16px;
                                   background: #2d2d2d;
                                   color: #ffffff;
                                   border: 1px solid #404040;
                                   border-radius: 4px; cursor: pointer;">
                        总体
                    </button>
            
                </div>
            </div>
            <div id="trend-chart-deqing-078" style="width: 100%; height: 600px;"></div>
        </div>

        <script>
            // 时间戳数据
            var timestamps_deqing_078 = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63];

            // 趋势图数据
            var trendData_deqing_078 = {"0-50米": {"机动车-准确率": [87.5, 100.0, 87.5, 100.0, 87.5, 80.0, 75.0, 85.7, 100.0, 75.0, 100.0, 100.0, 100.0, 80.0, 100.0, 66.7, 83.3, 100.0, 100.0, 100.0, 83.3, 100.0, 75.0, 100.0, 77.8, 100.0, 100.0, 87.5, 100.0, 100.0, 100.0, 100.0, 83.3, 100.0, 83.3, 75.0, 100.0, 80.0, 75.0, 75.0, 100.0, 100.0, 100.0, 100.0, 80.0, 100.0, 75.0, 100.0, 75.0, 80.0, 80.0, 100.0, 83.3, 72.7, 100.0, 71.4, 100.0, 75.0, 100.0, 100.0, 100.0, 80.0], "机动车-检出率": [87.5, 66.7, 77.8, 66.7, 70.0, 57.1, 85.7, 66.7, 33.3, 75.0, 75.0, 60.0, 85.7, 57.1, 55.6, 75.0, 62.5, 80.0, 66.7, 33.3, 83.3, 71.4, 75.0, 66.7, 70.0, 80.0, 66.7, 77.8, 66.7, 66.7, 55.6, 66.7, 71.4, 50.0, 55.6, 85.7, 62.5, 57.1, 50.0, 66.7, 50.0, 66.7, 66.7, 60.0, 80.0, 75.0, 75.0, 66.7, 50.0, 57.1, 66.7, 66.7, 83.3, 80.0, 70.0, 55.6, 50.0, 50.0, 75.0, 50.0, 80.0, 57.1], "行人-准确率": [100.0, 100.0, 100.0, 100.0, 100.0, 87.5, 100.0, 87.5, 100.0, 100.0, 100.0, 100.0, 90.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 87.5, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 85.7, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 87.5, 100.0, 87.5, 100.0, 83.3, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 88.9, 88.9, 100.0, 100.0, 88.9, 100.0, 100.0, 100.0, 100.0], "行人-检出率": [80.0, 83.3, 66.7, 77.8, 83.3, 70.0, 75.0, 77.8, 66.7, 83.3, 70.0, 75.0, 90.0, 71.4, 83.3, 77.8, 90.0, 83.3, 75.0, 77.8, 66.7, 88.9, 75.0, 80.0, 80.0, 75.0, 66.7, 80.0, 85.7, 75.0, 80.0, 75.0, 66.7, 75.0, 66.7, 80.0, 75.0, 83.3, 70.0, 83.3, 87.5, 75.0, 77.8, 75.0, 83.3, 71.4, 80.0, 66.7, 75.0, 66.7, 66.7, 66.7, 75.0, 88.9, 88.9, 66.7, 60.0, 80.0, 60.0, 66.7, 60.0, 85.7], "非机动车-准确率": [80.0, 83.3, 100.0, 100.0, 83.3, 83.3, 100.0, 100.0, 100.0, 100.0, 75.0, 75.0, 100.0, 100.0, 80.0, 100.0, 100.0, 80.0, 66.7, 100.0, 85.7, 75.0, 100.0, 71.4, 66.7, 100.0, 66.7, 100.0, 71.4, 83.3, 100.0, 75.0, 66.7, 80.0, 75.0, 71.4, 100.0, 75.0, 75.0, 100.0, 77.8, 77.8, 85.7, 71.4, 77.8, 85.7, 66.7, 100.0, 100.0, 100.0, 80.0, 100.0, 83.3, 62.5, 66.7, 100.0, 83.3, 100.0, 87.5, 66.7, 75.0, 100.0], "非机动车-检出率": [50.0, 55.6, 57.1, 60.0, 55.6, 62.5, 50.0, 75.0, 50.0, 66.7, 42.9, 60.0, 60.0, 50.0, 80.0, 33.3, 40.0, 80.0, 66.7, 62.5, 75.0, 60.0, 75.0, 62.5, 80.0, 33.3, 66.7, 55.6, 71.4, 50.0, 60.0, 60.0, 50.0, 80.0, 60.0, 71.4, 71.4, 60.0, 75.0, 62.5, 70.0, 77.8, 66.7, 71.4, 77.8, 66.7, 66.7, 57.1, 66.7, 66.7, 80.0, 50.0, 71.4, 71.4, 50.0, 50.0, 55.6, 50.0, 77.8, 75.0, 50.0, 71.4]}, "50-100米": {"机动车-准确率": [87.5, 66.7, 66.7, 66.7, 75.0, 70.0, 100.0, 66.7, 100.0, 75.0, 53.8, 71.4, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 80.0, 100.0, 100.0, 100.0, 80.0, 75.0, 66.7, 66.7, 100.0, 60.0, 62.5, 100.0, 57.1, 100.0, 83.3, 66.7, 100.0, 80.0, 100.0, 100.0, 75.0, 71.4, 100.0, 60.0, 71.4, 66.7, 75.0, 100.0, 100.0, 80.0, 72.7, 100.0, 80.0, 57.1, 100.0, 77.8, 75.0, 100.0, 66.7, 85.7, 100.0, 100.0, 100.0, 85.7], "机动车-检出率": [77.8, 66.7, 66.7, 50.0, 50.0, 70.0, 42.9, 40.0, 66.7, 60.0, 70.0, 83.3, 25.0, 33.3, 42.9, 33.3, 60.0, 50.0, 66.7, 33.3, 40.0, 25.0, 66.7, 37.5, 50.0, 50.0, 60.0, 60.0, 50.0, 71.4, 44.4, 33.3, 71.4, 57.1, 40.0, 57.1, 50.0, 33.3, 60.0, 71.4, 40.0, 60.0, 62.5, 80.0, 42.9, 33.3, 50.0, 57.1, 80.0, 25.0, 57.1, 44.4, 50.0, 70.0, 75.0, 33.3, 50.0, 60.0, 66.7, 66.7, 44.4, 66.7], "行人-准确率": [100.0, 100.0, 71.4, 100.0, 100.0, 72.7, 100.0, 100.0, 100.0, 72.7, 83.3, 85.7, 83.3, 80.0, 83.3, 100.0, 72.7, 100.0, 100.0, 100.0, 70.0, 75.0, 100.0, 100.0, 75.0, 88.9, 100.0, 72.7, 100.0, 100.0, 83.3, 88.9, 83.3, 100.0, 100.0, 100.0, 100.0, 80.0, 100.0, 72.7, 70.0, 75.0, 83.3, 100.0, 87.5, 100.0, 100.0, 100.0, 100.0, 88.9, 66.7, 80.0, 100.0, 100.0, 75.0, 100.0, 85.7, 100.0, 75.0, 83.3, 100.0, 66.7], "行人-检出率": [70.0, 66.7, 71.4, 75.0, 66.7, 88.9, 60.0, 66.7, 57.1, 80.0, 71.4, 75.0, 83.3, 66.7, 62.5, 50.0, 80.0, 60.0, 50.0, 60.0, 77.8, 75.0, 33.3, 60.0, 75.0, 80.0, 66.7, 80.0, 50.0, 33.3, 83.3, 80.0, 71.4, 55.6, 66.7, 80.0, 50.0, 80.0, 50.0, 80.0, 70.0, 60.0, 83.3, 66.7, 70.0, 80.0, 66.7, 80.0, 60.0, 80.0, 57.1, 88.9, 60.0, 71.4, 66.7, 60.0, 60.0, 80.0, 60.0, 62.5, 77.8, 85.7], "非机动车-准确率": [66.7, 100.0, 83.3, 66.7, 62.5, 60.0, 50.0, 80.0, 85.7, 55.6, 50.0, 66.7, 66.7, 83.3, 100.0, 100.0, 100.0, 83.3, 71.4, 55.6, 50.0, 50.0, 100.0, 50.0, 66.7, 66.7, 50.0, 62.5, 75.0, 71.4, 100.0, 66.7, 50.0, 80.0, 60.0, 75.0, 100.0, 100.0, 60.0, 100.0, 50.0, 60.0, 100.0, 100.0, 83.3, 100.0, 100.0, 50.0, 100.0, 50.0, 75.0, 100.0, 63.6, 66.7, 66.7, 66.7, 66.7, 66.7, 85.7, 66.7, 83.3, 100.0], "非机动车-检出率": [66.7, 33.3, 62.5, 50.0, 71.4, 75.0, 42.9, 50.0, 60.0, 55.6, 66.7, 50.0, 28.6, 55.6, 50.0, 60.0, 50.0, 50.0, 50.0, 50.0, 40.0, 77.8, 37.5, 60.0, 40.0, 66.7, 50.0, 62.5, 60.0, 55.6, 40.0, 66.7, 57.1, 66.7, 60.0, 42.9, 25.0, 33.3, 50.0, 60.0, 50.0, 75.0, 40.0, 50.0, 50.0, 33.3, 57.1, 57.1, 50.0, 44.4, 60.0, 42.9, 70.0, 44.4, 28.6, 50.0, 50.0, 57.1, 60.0, 50.0, 50.0, 40.0]}, "总体": {"机动车-准确率": [87.5, 83.3, 77.1, 83.3, 81.2, 75.0, 87.5, 76.2, 100.0, 75.0, 76.9, 85.7, 100.0, 90.0, 100.0, 83.3, 91.7, 100.0, 90.0, 100.0, 91.7, 100.0, 77.5, 87.5, 72.2, 83.3, 100.0, 73.8, 81.2, 100.0, 78.6, 100.0, 83.3, 83.3, 91.7, 77.5, 100.0, 90.0, 75.0, 73.2, 100.0, 80.0, 85.7, 83.3, 77.5, 100.0, 87.5, 90.0, 73.9, 90.0, 80.0, 78.6, 91.7, 75.3, 87.5, 85.7, 83.3, 80.4, 100.0, 100.0, 100.0, 82.9], "机动车-检出率": [82.6, 66.7, 72.2, 58.3, 60.0, 63.6, 64.3, 53.3, 50.0, 67.5, 72.5, 71.7, 55.4, 45.2, 49.2, 54.2, 61.3, 65.0, 66.7, 33.3, 61.7, 48.2, 70.8, 52.1, 60.0, 65.0, 63.3, 68.9, 58.3, 69.0, 50.0, 50.0, 71.4, 53.6, 47.8, 71.4, 56.2, 45.2, 55.0, 69.0, 45.0, 63.3, 64.6, 70.0, 61.4, 54.2, 62.5, 61.9, 65.0, 41.1, 61.9, 55.6, 66.7, 75.0, 72.5, 44.4, 50.0, 55.0, 70.8, 58.3, 62.2, 61.9], "行人-准确率": [100.0, 100.0, 85.7, 100.0, 100.0, 80.1, 100.0, 93.8, 100.0, 86.4, 91.7, 92.9, 86.7, 90.0, 91.7, 100.0, 86.4, 100.0, 100.0, 93.8, 85.0, 87.5, 100.0, 100.0, 87.5, 94.4, 100.0, 86.4, 100.0, 100.0, 91.7, 87.3, 91.7, 100.0, 100.0, 100.0, 100.0, 90.0, 100.0, 86.4, 78.8, 87.5, 85.4, 100.0, 85.4, 100.0, 100.0, 100.0, 100.0, 94.4, 83.3, 90.0, 100.0, 94.4, 81.9, 100.0, 92.9, 94.4, 87.5, 91.7, 100.0, 83.3], "行人-检出率": [75.0, 75.0, 69.0, 76.4, 75.0, 79.4, 67.5, 72.2, 61.9, 81.7, 70.7, 75.0, 86.7, 69.0, 72.9, 63.9, 85.0, 71.7, 62.5, 68.9, 72.2, 81.9, 54.2, 70.0, 77.5, 77.5, 66.7, 80.0, 67.9, 54.2, 81.7, 77.5, 69.0, 65.3, 66.7, 80.0, 62.5, 81.7, 60.0, 81.7, 78.8, 67.5, 80.6, 70.8, 76.7, 75.7, 73.3, 73.3, 67.5, 73.3, 61.9, 77.8, 67.5, 80.2, 77.8, 63.3, 60.0, 80.0, 60.0, 64.6, 68.9, 85.7], "非机动车-准确率": [73.3, 91.7, 91.7, 83.3, 72.9, 71.7, 75.0, 90.0, 92.9, 77.8, 62.5, 70.8, 83.3, 91.7, 90.0, 100.0, 100.0, 81.7, 69.0, 77.8, 67.9, 62.5, 100.0, 60.7, 66.7, 83.3, 58.3, 81.2, 73.2, 77.4, 100.0, 70.8, 58.3, 80.0, 67.5, 73.2, 100.0, 87.5, 67.5, 100.0, 63.9, 68.9, 92.9, 85.7, 80.6, 92.9, 83.3, 75.0, 100.0, 75.0, 77.5, 100.0, 73.5, 64.6, 66.7, 83.3, 75.0, 83.3, 86.6, 66.7, 79.2, 100.0], "非机动车-检出率": [58.3, 44.4, 59.8, 55.0, 63.5, 68.8, 46.4, 62.5, 55.0, 61.1, 54.8, 55.0, 44.3, 52.8, 65.0, 46.7, 45.0, 65.0, 58.3, 56.2, 57.5, 68.9, 56.2, 61.3, 60.0, 50.0, 58.3, 59.0, 65.7, 52.8, 50.0, 63.3, 53.6, 73.3, 60.0, 57.1, 48.2, 46.7, 62.5, 61.3, 60.0, 76.4, 53.3, 60.7, 63.9, 50.0, 61.9, 57.1, 58.3, 55.6, 70.0, 46.4, 70.7, 57.9, 39.3, 50.0, 52.8, 53.6, 68.9, 62.5, 50.0, 55.7]}};

            // 颜色配置
            var categoryColors_deqing_078 = {
                '行人': ['#1f77b4', '#aec7e8'],
                '机动车': ['#2ca02c', '#98df8a'],
                '非机动车': ['#ff7f0e', '#ffbb78']
            };

            // 趋势图配置
            function getTrendOption_deqing_078(distBin) {
                var seriesData = trendData_deqing_078[distBin];
                var series = [];

                Object.keys(seriesData).forEach(function(seriesName) {
                    var category = seriesName.split('-')[0];
                    var metric = seriesName.split('-')[1];
                    var colorIndex = metric === '准确率' ? 0 : 1;
                    var lineType = metric === '准确率' ? 'solid' : 'dashed';

                    series.push({
                        name: seriesName,
                        type: 'line',
                        data: seriesData[seriesName],
                        smooth: false,
                        symbol: 'none',
                        lineStyle: {
                            color: categoryColors_deqing_078[category][colorIndex],
                            width: 3,
                            type: lineType
                        },
                        emphasis: {
                            focus: 'series',
                            lineStyle: {
                                width: 4
                            }
                        }
                    });
                });

                return {
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    title: {
                        text: distBin + ' - 性能趋势',
                        left: 'center',
                        textStyle: {
                            color: '#5a6c7d',
                            fontSize: 16
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        borderColor: '#bdc3c7',
                        textStyle: {
                            color: '#2c3e50'
                        }
                    },
                    legend: {
                        top: '8%',
                        textStyle: {
                            color: '#5a6c7d'
                        }
                    },
                    grid: {
                        left: '10%',
                        right: '10%',
                        top: '20%',
                        bottom: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: timestamps_deqing_078,
                        name: '帧号',
                        axisLabel: {
                            color: '#7f8c8d'
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#bdc3c7'
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '性能指标 (%)',
                        min: 0,
                        max: 100,
                        axisLabel: {
                            color: '#7f8c8d'
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#bdc3c7'
                            }
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#ecf0f1',
                                type: 'dashed'
                            }
                        }
                    },
                    series: series
                };
            }

            // 初始化趋势图
            var trendChart_deqing_078 = echarts.init(document.getElementById('trend-chart-deqing-078'));
            trendChart_deqing_078.setOption(getTrendOption_deqing_078('0-50米'));

            // 切换函数
            function switchTrend_deqing_078(distBin, clickedButton) {
                // 更新按钮状态
                var buttons = document.querySelectorAll('#trend-buttons-deqing-078 .trend-btn');
                buttons.forEach(btn => btn.classList.remove('trend-active'));
                clickedButton.classList.add('trend-active');

                // 更新趋势图
                trendChart_deqing_078.setOption(getTrendOption_deqing_078(distBin));
            }
        </script>

        <style>
            .trend-btn.trend-active {
                background: #00d4ff !important;
                color: #000000 !important;
            }
            .trend-btn:hover {
                background: #00d4ff !important;
                color: #000000 !important;
            }
        </style>
        
                </div>
            </body>
            </html>
            