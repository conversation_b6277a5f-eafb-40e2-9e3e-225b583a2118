# -*- coding: utf-8 -*-
"""
单场景图表模块 - 商务科技风格
实现指针式表盘、分组柱状图、雷达图+切换、时间趋势图+切换
"""

import pandas as pd
import numpy as np
import json
from typing import Dict, List, Any, Optional, Tuple
from pyecharts import options as opts
from pyecharts.charts import Gauge, Bar, Line, Page, Grid
from pyecharts.globals import ThemeType
import logging

try:
    from .theme import ChartTheme
except ImportError:
    from theme import ChartTheme

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SingleSceneCharts:
    """单场景图表生成器 - 商务科技风格"""

    def __init__(self, theme: ChartTheme):
        """
        初始化图表生成器

        Args:
            theme: 图表主题配置
        """
        self.theme = theme
        self.color_palette = theme.get_color_palette()
        self.category_colors = theme.get_category_colors()
        self.metric_colors = theme.get_metric_colors()
        self.gauge_colors = theme.get_gauge_colors()
        
    def convert_numpy_types(self, obj):
        """递归转换numpy数据类型为原生Python类型"""
        if isinstance(obj, dict):
            return {key: self.convert_numpy_types(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self.convert_numpy_types(item) for item in obj]
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        else:
            return obj

    def create_gauge_charts(self, scene_data: pd.DataFrame, scene_name: str) -> Tuple[Gauge, Gauge, Gauge]:
        """
        创建3个指针式表盘：检出率、准确率、F1分数

        Args:
            scene_data: 单场景聚合数据
            scene_name: 场景名称

        Returns:
            三个Gauge图表对象的元组
        """
        logger.info(f"创建场景 {scene_name} 的指针式表盘")

        # 计算总体指标
        total_tp = scene_data['tp'].sum()
        total_fp = scene_data['fp'].sum()
        total_fn = scene_data['fn'].sum()

        overall_recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0
        overall_precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0
        overall_f1 = 2 * (overall_precision * overall_recall) / (overall_precision + overall_recall) if (overall_precision + overall_recall) > 0 else 0

        # 转换为百分比
        recall_pct = overall_recall * 100
        precision_pct = overall_precision * 100
        f1_pct = overall_f1 * 100

        def create_gauge_html(title: str, value: float, position: str) -> str:
            """创建单个表盘的HTML"""
            # 根据值确定颜色
            if value >= 80:
                color_stops = [[0.3, '#e67e22'], [0.7, '#f39c12'], [1, '#2ecc71']]
                detail_color = '#2ecc71'
            elif value >= 60:
                color_stops = [[0.3, '#e67e22'], [0.7, '#3498db'], [1, '#3498db']]
                detail_color = '#3498db'
            else:
                color_stops = [[0.3, '#e67e22'], [0.7, '#e67e22'], [1, '#e67e22']]
                detail_color = '#e67e22'

            return f"""
            {{
                name: '{title}',
                type: 'gauge',
                center: ['{position}', '50%'],
                radius: '70%',
                min: 0,
                max: 100,
                splitNumber: 10,
                axisLine: {{
                    lineStyle: {{
                        color: {color_stops},
                        width: 8
                    }}
                }},
                pointer: {{
                    itemStyle: {{ color: 'auto' }}
                }},
                axisTick: {{
                    distance: -30,
                    length: 8,
                    lineStyle: {{ color: '#fff', width: 2 }}
                }},
                splitLine: {{
                    distance: -30,
                    length: 30,
                    lineStyle: {{ color: '#fff', width: 4 }}
                }},
                axisLabel: {{
                    color: 'auto',
                    distance: 40,
                    fontSize: 12
                }},
                detail: {{
                    valueAnimation: true,
                    formatter: '{{value}}%',
                    color: '{detail_color}',
                    fontSize: 16,
                    offsetCenter: [0, '70%']
                }},
                title: {{
                    offsetCenter: [0, '90%'],
                    fontSize: 14,
                    color: '#5a6c7d'
                }},
                data: [{{ value: {value:.1f}, name: '{title}' }}]
            }}
            """

        # 创建双表盘HTML
        gauge_html = f"""
        <div id="gauge-container-{scene_name.replace(' ', '-')}" style="width: 100%; height: 400px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: #2c3e50; margin-bottom: 15px;">
                    {scene_name} - 总体性能仪表盘
                </h3>
            </div>
            <div id="gauge-chart-{scene_name.replace(' ', '-')}" style="width: 100%; height: 350px;"></div>
        </div>

        <script>
            // 初始化仪表盘
            var gaugeChart_{scene_name.replace(' ', '-').replace('-', '_')} = echarts.init(document.getElementById('gauge-chart-{scene_name.replace(' ', '-')}'));

            var gaugeOption_{scene_name.replace(' ', '-').replace('-', '_')} = {{
                backgroundColor: 'rgba(255, 255, 255, 0.5)',
                series: [
                    {create_gauge_html("检出率", recall_pct, "25%")},
                    {create_gauge_html("准确率", precision_pct, "75%")}
                ]
            }};

            gaugeChart_{scene_name.replace(' ', '-').replace('-', '_')}.setOption(gaugeOption_{scene_name.replace(' ', '-').replace('-', '_')});
            window.addEventListener('resize', () => gaugeChart_{scene_name.replace(' ', '-').replace('-', '_')}.resize());
        </script>
        """

        return gauge_html

    def create_grouped_bar_chart(self, scene_data: pd.DataFrame, scene_name: str) -> Bar:
        """
        创建分组柱状图：4个距离段组，每组6根柱子

        Args:
            scene_data: 单场景数据
            scene_name: 场景名称

        Returns:
            Bar图表对象
        """
        logger.info(f"创建场景 {scene_name} 的分组柱状图")

        # 按距离段和类别聚合数据
        grouped_data = scene_data.groupby(['distance_bin', 'target_cls_name']).agg({
            'recall': 'mean',
            'precision': 'mean'
        }).reset_index()

        # 获取距离段和类别列表
        distance_bins = sorted(grouped_data['distance_bin'].unique())
        categories = sorted(grouped_data['target_cls_name'].unique())

        # 创建柱状图
        bar = Bar(init_opts=opts.InitOpts(
            width="1000px",
            height="500px",
            bg_color=self.theme.chart_theme.get('background_color', '#1e1e1e')
        ))

        # 准备X轴数据（距离段）
        bar.add_xaxis(distance_bins)

        # 为每个类别的每个指标添加数据系列
        for category in categories:
            cat_colors = self.category_colors.get(category, {'primary': '#ffffff', 'secondary': '#cccccc'})

            # 准确率数据（实线效果）
            precision_values = []
            for dist in distance_bins:
                subset = grouped_data[
                    (grouped_data['distance_bin'] == dist) &
                    (grouped_data['target_cls_name'] == category)
                ]
                if not subset.empty:
                    precision_values.append(round(subset['precision'].iloc[0] * 100, 1))
                else:
                    precision_values.append(0)

            bar.add_yaxis(
                series_name=f"{category}-准确率",
                y_axis=precision_values,
                color=cat_colors['primary'],
                label_opts=opts.LabelOpts(is_show=True, position="top", color="#ffffff"),
            )

            # 检出率数据（用不同透明度区分）
            recall_values = []
            for dist in distance_bins:
                subset = grouped_data[
                    (grouped_data['distance_bin'] == dist) &
                    (grouped_data['target_cls_name'] == category)
                ]
                if not subset.empty:
                    recall_values.append(round(subset['recall'].iloc[0] * 100, 1))
                else:
                    recall_values.append(0)

            bar.add_yaxis(
                series_name=f"{category}-检出率",
                y_axis=recall_values,
                color=cat_colors['secondary'],
                label_opts=opts.LabelOpts(is_show=True, position="top", color="#ffffff"),
            )

        # 设置全局配置
        bar.set_global_opts(
            title_opts=opts.TitleOpts(
                title=f"{scene_name} - 距离段性能对比",
                subtitle="不同目标类别在各距离段的准确率和检出率对比",
                title_textstyle_opts=opts.TextStyleOpts(
                    color=self.theme.chart_theme.get('text_color', '#ffffff'),
                    font_size=16
                ),
                subtitle_textstyle_opts=opts.TextStyleOpts(
                    color=self.theme.chart_theme.get('secondary_text', '#cccccc'),
                    font_size=12
                )
            ),
            xaxis_opts=opts.AxisOpts(
                name="距离段",
                axislabel_opts=opts.LabelOpts(color=self.theme.chart_theme.get('text_color', '#ffffff')),
                axisline_opts=opts.AxisLineOpts(
                    linestyle_opts=opts.LineStyleOpts(color=self.theme.chart_theme.get('grid_color', '#404040'))
                )
            ),
            yaxis_opts=opts.AxisOpts(
                name="性能指标 (%)",
                min_=0,
                max_=100,
                axislabel_opts=opts.LabelOpts(color=self.theme.chart_theme.get('text_color', '#ffffff')),
                axisline_opts=opts.AxisLineOpts(
                    linestyle_opts=opts.LineStyleOpts(color=self.theme.chart_theme.get('grid_color', '#404040'))
                ),
                splitline_opts=opts.SplitLineOpts(
                    is_show=True,
                    linestyle_opts=opts.LineStyleOpts(
                        color=self.theme.chart_theme.get('grid_color', '#404040'),
                        type_="dashed"
                    )
                )
            ),
            legend_opts=opts.LegendOpts(
                pos_top="8%",
                textstyle_opts=opts.TextStyleOpts(color=self.theme.chart_theme.get('text_color', '#ffffff'))
            ),
            tooltip_opts=opts.TooltipOpts(
                trigger="axis",
                background_color="rgba(45, 45, 45, 0.95)",
                border_color=self.theme.chart_theme.get('grid_color', '#404040'),
                textstyle_opts=opts.TextStyleOpts(color="#ffffff")
            ),
        )

        return bar

    def create_radar_chart_with_switch(self, scene_data: pd.DataFrame, scene_name: str) -> str:
        """
        创建雷达图+切换按钮的HTML组合

        Args:
            scene_data: 单场景数据
            scene_name: 场景名称

        Returns:
            包含雷达图和切换按钮的HTML字符串
        """
        logger.info(f"创建场景 {scene_name} 的雷达图+切换")

        # 按距离段和类别聚合数据
        radar_data = scene_data.groupby(['distance_bin', 'target_cls_name']).agg({
            'recall': 'mean',
            'precision': 'mean'
        }).reset_index()

        # 获取距离段列表
        distance_bins = sorted(radar_data['distance_bin'].unique())
        categories = sorted(radar_data['target_cls_name'].unique())

        # 为每个距离段创建雷达图数据
        radar_charts_data = {}

        for dist_bin in distance_bins:
            dist_data = radar_data[radar_data['distance_bin'] == dist_bin]

            # 6个维度的数据
            radar_values = []
            for category in categories:
                cat_data = dist_data[dist_data['target_cls_name'] == category]
                if not cat_data.empty:
                    precision_val = cat_data['precision'].iloc[0] * 100
                    recall_val = cat_data['recall'].iloc[0] * 100
                else:
                    precision_val = 0
                    recall_val = 0

                radar_values.extend([precision_val, recall_val])

            radar_charts_data[dist_bin] = radar_values

        # 生成HTML（包含JavaScript切换逻辑）
        scene_id = scene_name.replace(' ', '-').replace('场景', 'scene')

        html_content = f"""
        <div id="radar-container-{scene_id}" style="width: 100%; height: 600px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: {self.theme.chart_theme.get('text_color', '#ffffff')}; margin-bottom: 15px;">
                    {scene_name} - 雷达图分析
                </h3>
                <div id="radar-buttons-{scene_id}" style="margin-bottom: 20px;">
        """

        # 添加切换按钮
        for i, dist_bin in enumerate(distance_bins):
            active_class = "radar-active" if i == 0 else ""
            html_content += f"""
                    <button onclick="switchRadar_{scene_id.replace('-', '_')}('{dist_bin}')"
                            class="radar-btn {active_class}"
                            style="margin: 0 10px; padding: 8px 16px;
                                   background: {self.theme.chart_theme.get('secondary_bg', '#2d2d2d')};
                                   color: {self.theme.chart_theme.get('text_color', '#ffffff')};
                                   border: 1px solid {self.theme.chart_theme.get('grid_color', '#404040')};
                                   border-radius: 4px; cursor: pointer;">
                        {dist_bin}
                    </button>
            """

        html_content += f"""
                </div>
            </div>
            <div id="radar-chart-{scene_id}" style="width: 100%; height: 500px;"></div>
        </div>

        <script>
            // 雷达图数据
            var radarData_{scene_id.replace('-', '_')} = {json.dumps(self.convert_numpy_types(radar_charts_data), ensure_ascii=False)};

            // 雷达图配置
            var radarOption_{scene_id.replace('-', '_')} = {{
                backgroundColor: '{self.theme.chart_theme.get('background_color', '#1e1e1e')}',
                radar: {{
                    indicator: [
                        {{name: '人-准确率', max: 100}},
                        {{name: '人-检出率', max: 100}},
                        {{name: '车-准确率', max: 100}},
                        {{name: '车-检出率', max: 100}},
                        {{name: '非-准确率', max: 100}},
                        {{name: '非-检出率', max: 100}}
                    ],
                    center: ['50%', '50%'],
                    radius: '70%',
                    axisName: {{
                        color: '{self.theme.chart_theme.get('text_color', '#ffffff')}',
                        fontSize: 12
                    }},
                    splitLine: {{
                        lineStyle: {{
                            color: '{self.theme.chart_theme.get('grid_color', '#404040')}'
                        }}
                    }},
                    splitArea: {{
                        show: false
                    }}
                }},
                series: [{{
                    type: 'radar',
                    data: [{{
                        value: radarData_{scene_id.replace('-', '_')}['{distance_bins[0]}'],
                        name: '{distance_bins[0]}',
                        areaStyle: {{
                            color: 'rgba(0, 212, 255, 0.3)'
                        }},
                        lineStyle: {{
                            color: '{self.theme.chart_theme.get('accent_color', '#00d4ff')}'
                        }}
                    }}]
                }}]
            }};

            // 初始化雷达图
            var radarChart_{scene_id.replace('-', '_')} = echarts.init(document.getElementById('radar-chart-{scene_id}'));
            radarChart_{scene_id.replace('-', '_')}.setOption(radarOption_{scene_id.replace('-', '_')});

            // 切换函数
            function switchRadar_{scene_id.replace('-', '_')}(distBin) {{
                // 更新按钮状态
                var buttons = document.querySelectorAll('#radar-buttons-{scene_id} .radar-btn');
                buttons.forEach(btn => btn.classList.remove('radar-active'));
                event.target.classList.add('radar-active');

                // 更新雷达图数据
                radarOption_{scene_id.replace('-', '_')}.series[0].data[0].value = radarData_{scene_id.replace('-', '_')}[distBin];
                radarOption_{scene_id.replace('-', '_')}.series[0].data[0].name = distBin;
                radarChart_{scene_id.replace('-', '_')}.setOption(radarOption_{scene_id.replace('-', '_')});
            }}
        </script>

        <style>
            .radar-btn.radar-active {{
                background: {self.theme.chart_theme.get('accent_color', '#00d4ff')} !important;
                color: #000000 !important;
            }}
            .radar-btn:hover {{
                background: {self.theme.chart_theme.get('accent_color', '#00d4ff')} !important;
                color: #000000 !important;
            }}
        </style>
        """

        return html_content

    def create_time_trend_with_switch(self, scene_data: pd.DataFrame, scene_name: str) -> str:
        """
        创建时间趋势图+切换按钮的HTML组合

        Args:
            scene_data: 单场景数据
            scene_name: 场景名称

        Returns:
            包含趋势图和切换按钮的HTML字符串
        """
        logger.info(f"创建场景 {scene_name} 的时间趋势图+切换")

        # 检查是否有timestamp字段，如果没有则创建
        if 'timestamp' not in scene_data.columns:
            scene_data = scene_data.copy()
            scene_data['timestamp'] = range(1, len(scene_data) + 1)

        # 按时间戳、距离段和类别聚合数据
        trend_data = scene_data.groupby(['timestamp', 'distance_bin', 'target_cls_name']).agg({
            'recall': 'mean',
            'precision': 'mean'
        }).reset_index()

        # 添加"总体"距离段数据
        overall_data = scene_data.groupby(['timestamp', 'target_cls_name']).agg({
            'recall': 'mean',
            'precision': 'mean'
        }).reset_index()
        overall_data['distance_bin'] = '总体'

        # 合并原始数据和总体数据
        trend_data = pd.concat([trend_data, overall_data], ignore_index=True)

        # 获取距离段和类别列表
        distance_bins = sorted(trend_data['distance_bin'].unique())
        categories = sorted(trend_data['target_cls_name'].unique())
        timestamps = sorted(trend_data['timestamp'].unique())

        # 为每个距离段准备数据
        trend_charts_data = {}

        for dist_bin in distance_bins:
            dist_data = trend_data[trend_data['distance_bin'] == dist_bin]

            series_data = {}
            for category in categories:
                cat_data = dist_data[dist_data['target_cls_name'] == category]

                # 准确率时间序列
                precision_series = []
                recall_series = []

                for ts in timestamps:
                    ts_data = cat_data[cat_data['timestamp'] == ts]
                    if not ts_data.empty:
                        precision_series.append(round(ts_data['precision'].iloc[0] * 100, 1))
                        recall_series.append(round(ts_data['recall'].iloc[0] * 100, 1))
                    else:
                        precision_series.append(0)
                        recall_series.append(0)

                series_data[f"{category}-准确率"] = precision_series
                series_data[f"{category}-检出率"] = recall_series

            trend_charts_data[dist_bin] = series_data

        # 生成HTML
        scene_id = scene_name.replace(' ', '-').replace('场景', 'scene')

        html_content = f"""
        <div id="trend-container-{scene_id}" style="width: 100%; height: 700px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: {self.theme.chart_theme.get('text_color', '#ffffff')}; margin-bottom: 15px;">
                    {scene_name} - 时间趋势分析
                </h3>
                <div id="trend-buttons-{scene_id}" style="margin-bottom: 20px;">
        """

        # 添加切换按钮
        for i, dist_bin in enumerate(distance_bins):
            active_class = "trend-active" if i == 0 else ""
            html_content += f"""
                    <button onclick="switchTrend_{scene_id.replace('-', '_')}('{dist_bin}')"
                            class="trend-btn {active_class}"
                            style="margin: 0 10px; padding: 8px 16px;
                                   background: {self.theme.chart_theme.get('secondary_bg', '#2d2d2d')};
                                   color: {self.theme.chart_theme.get('text_color', '#ffffff')};
                                   border: 1px solid {self.theme.chart_theme.get('grid_color', '#404040')};
                                   border-radius: 4px; cursor: pointer;">
                        {dist_bin}
                    </button>
            """

        # 转换数据
        clean_timestamps = self.convert_numpy_types(timestamps)
        clean_trend_data = self.convert_numpy_types(trend_charts_data)

        html_content += f"""
                </div>
            </div>
            <div id="trend-chart-{scene_id}" style="width: 100%; height: 600px;"></div>
        </div>

        <script>
            // 时间戳数据
            var timestamps_{scene_id.replace('-', '_')} = {json.dumps(clean_timestamps, ensure_ascii=False)};

            // 趋势图数据
            var trendData_{scene_id.replace('-', '_')} = {json.dumps(clean_trend_data, ensure_ascii=False)};

            // 颜色配置
            var categoryColors_{scene_id.replace('-', '_')} = {{
                '行人': ['{self.category_colors.get('行人', {}).get('primary', '#1f77b4')}', '{self.category_colors.get('行人', {}).get('secondary', '#aec7e8')}'],
                '机动车': ['{self.category_colors.get('机动车', {}).get('primary', '#2ca02c')}', '{self.category_colors.get('机动车', {}).get('secondary', '#98df8a')}'],
                '非机动车': ['{self.category_colors.get('非机动车', {}).get('primary', '#ff7f0e')}', '{self.category_colors.get('非机动车', {}).get('secondary', '#ffbb78')}']
            }};

            // 趋势图配置
            function getTrendOption_{scene_id.replace('-', '_')}(distBin) {{
                var seriesData = trendData_{scene_id.replace('-', '_')}[distBin];
                var series = [];

                Object.keys(seriesData).forEach(function(seriesName) {{
                    var category = seriesName.split('-')[0];
                    var metric = seriesName.split('-')[1];
                    var colorIndex = metric === '准确率' ? 0 : 1;
                    var lineType = metric === '准确率' ? 'solid' : 'dashed';

                    series.push({{
                        name: seriesName,
                        type: 'line',
                        data: seriesData[seriesName],
                        smooth: false,
                        symbol: 'none',
                        lineStyle: {{
                            color: categoryColors_{scene_id.replace('-', '_')}[category][colorIndex],
                            width: 3,
                            type: lineType
                        }},
                        emphasis: {{
                            focus: 'series',
                            lineStyle: {{
                                width: 4
                            }}
                        }}
                    }});
                }});

                return {{
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    title: {{
                        text: distBin + ' - 性能趋势',
                        left: 'center',
                        textStyle: {{
                            color: '#5a6c7d',
                            fontSize: 16
                        }}
                    }},
                    tooltip: {{
                        trigger: 'axis',
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        borderColor: '#bdc3c7',
                        textStyle: {{
                            color: '#2c3e50'
                        }}
                    }},
                    legend: {{
                        top: '8%',
                        textStyle: {{
                            color: '#5a6c7d'
                        }}
                    }},
                    grid: {{
                        left: '10%',
                        right: '10%',
                        top: '20%',
                        bottom: '15%',
                        containLabel: true
                    }},
                    xAxis: {{
                        type: 'category',
                        data: timestamps_{scene_id.replace('-', '_')},
                        name: '帧号',
                        axisLabel: {{
                            color: '#7f8c8d'
                        }},
                        axisLine: {{
                            lineStyle: {{
                                color: '#bdc3c7'
                            }}
                        }}
                    }},
                    yAxis: {{
                        type: 'value',
                        name: '性能指标 (%)',
                        min: 0,
                        max: 100,
                        axisLabel: {{
                            color: '#7f8c8d'
                        }},
                        axisLine: {{
                            lineStyle: {{
                                color: '#bdc3c7'
                            }}
                        }},
                        splitLine: {{
                            lineStyle: {{
                                color: '#ecf0f1',
                                type: 'dashed'
                            }}
                        }}
                    }},
                    series: series
                }};
            }}

            // 初始化趋势图
            var trendChart_{scene_id.replace('-', '_')} = echarts.init(document.getElementById('trend-chart-{scene_id}'));
            trendChart_{scene_id.replace('-', '_')}.setOption(getTrendOption_{scene_id.replace('-', '_')}('{distance_bins[0]}'));

            // 切换函数
            function switchTrend_{scene_id.replace('-', '_')}(distBin) {{
                // 更新按钮状态
                var buttons = document.querySelectorAll('#trend-buttons-{scene_id} .trend-btn');
                buttons.forEach(btn => btn.classList.remove('trend-active'));
                event.target.classList.add('trend-active');

                // 更新趋势图
                trendChart_{scene_id.replace('-', '_')}.setOption(getTrendOption_{scene_id.replace('-', '_')}(distBin));
            }}
        </script>

        <style>
            .trend-btn.trend-active {{
                background: {self.theme.chart_theme.get('accent_color', '#00d4ff')} !important;
                color: #000000 !important;
            }}
            .trend-btn:hover {{
                background: {self.theme.chart_theme.get('accent_color', '#00d4ff')} !important;
                color: #000000 !important;
            }}
        </style>
        """

        return html_content
