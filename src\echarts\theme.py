"""
图表主题配置模块 - 商务科技风格
定义深色主题的配色方案、字体样式和图表主题
"""

from typing import Dict, Any, List


class ChartTheme:
    """商务科技风格图表主题配置类"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化主题配置

        Args:
            config: 配置字典
        """
        self.config = config
        self.color_palette = config.get('color_palette', {})
        self.chart_theme = config.get('chart_theme', {})
        
    def get_color_palette(self) -> List[str]:
        """获取基础颜色调色板"""
        return [
            self.color_palette.get('person', {}).get('primary', '#1f77b4'),
            self.color_palette.get('vehicle', {}).get('primary', '#2ca02c'),
            self.color_palette.get('non_vehicle', {}).get('primary', '#ff7f0e'),
            self.color_palette.get('recall', '#00d4ff'),
            self.color_palette.get('precision', '#00ff88'),
            self.color_palette.get('f1', '#ff6b35'),
        ]

    def get_category_colors(self) -> Dict[str, Dict[str, str]]:
        """获取目标类别对应的颜色（主色和次色）"""
        return {
            '机动车': {
                'primary': self.color_palette.get('vehicle', {}).get('primary', '#2ca02c'),
                'secondary': self.color_palette.get('vehicle', {}).get('secondary', '#98df8a')
            },
            '非机动车': {
                'primary': self.color_palette.get('non_vehicle', {}).get('primary', '#ff7f0e'),
                'secondary': self.color_palette.get('non_vehicle', {}).get('secondary', '#ffbb78')
            },
            '行人': {
                'primary': self.color_palette.get('person', {}).get('primary', '#1f77b4'),
                'secondary': self.color_palette.get('person', {}).get('secondary', '#aec7e8')
            },
        }

    def get_metric_colors(self) -> Dict[str, str]:
        """获取指标对应的颜色"""
        return {
            'recall': self.color_palette.get('recall', '#00d4ff'),
            'precision': self.color_palette.get('precision', '#00ff88'),
            'f1': self.color_palette.get('f1', '#ff6b35'),
        }

    def get_gauge_colors(self) -> Dict[str, str]:
        """获取表盘颜色阈值"""
        return {
            'good': self.color_palette.get('gauge_good', '#00ff88'),      # >80%
            'medium': self.color_palette.get('gauge_medium', '#ffaa00'),  # 60%-80%
            'poor': self.color_palette.get('gauge_poor', '#ff4444'),      # <60%
        }
    
    def get_global_opts(self) -> Dict[str, Any]:
        """获取深色主题全局配置选项"""
        return {
            'background_color': self.chart_theme.get('background_color', '#1e1e1e'),
            'text_style': {
                'color': self.chart_theme.get('text_color', '#ffffff'),
                'font_family': self.chart_theme.get('font_family', 'Microsoft YaHei, Arial, sans-serif'),
                'font_size': self.chart_theme.get('font_size', 12),
            },
            'title_opts': {
                'text_style': {
                    'color': self.chart_theme.get('text_color', '#ffffff'),
                    'font_family': self.chart_theme.get('font_family', 'Microsoft YaHei, Arial, sans-serif'),
                    'font_size': 16,
                    'font_weight': 'bold',
                }
            },
            'legend_opts': {
                'text_style': {
                    'color': self.chart_theme.get('text_color', '#ffffff'),
                    'font_family': self.chart_theme.get('font_family', 'Microsoft YaHei, Arial, sans-serif'),
                    'font_size': 12,
                }
            },
            'tooltip_opts': {
                'background_color': 'rgba(45, 45, 45, 0.95)',  # 深色背景
                'border_color': self.chart_theme.get('grid_color', '#404040'),
                'border_width': 1,
                'text_style': {
                    'color': self.chart_theme.get('text_color', '#ffffff'),
                    'font_size': 12,
                }
            }
        }
    
    def get_grid_opts(self) -> Dict[str, Any]:
        """获取网格配置"""
        return {
            'left': '10%',
            'right': '10%',
            'top': '15%',
            'bottom': '15%',
            'containLabel': True,
        }
    
    def get_axis_opts(self) -> Dict[str, Any]:
        """获取深色主题坐标轴配置"""
        return {
            'axisline_opts': {
                'linestyle_opts': {
                    'color': self.chart_theme.get('grid_color', '#404040'),
                    'width': 1,
                }
            },
            'axistick_opts': {
                'is_show': True,
                'linestyle_opts': {
                    'color': self.chart_theme.get('grid_color', '#404040'),
                }
            },
            'axislabel_opts': {
                'color': self.chart_theme.get('text_color', '#ffffff'),
                'font_size': 11,
            },
            'splitline_opts': {
                'is_show': True,
                'linestyle_opts': {
                    'color': self.chart_theme.get('grid_color', '#404040'),
                    'width': 1,
                    'type_': 'dashed',
                }
            }
        }
    
    def get_kpi_card_style(self) -> Dict[str, Any]:
        """获取深色主题KPI卡片样式"""
        return {
            'background_color': self.chart_theme.get('secondary_bg', '#2d2d2d'),
            'border_color': self.chart_theme.get('grid_color', '#404040'),
            'border_width': 1,
            'border_radius': 8,
            'padding': 20,
            'title_color': self.chart_theme.get('secondary_text', '#cccccc'),
            'value_color': self.chart_theme.get('accent_color', '#00d4ff'),
            'font_family': self.chart_theme.get('font_family', 'Microsoft YaHei, Arial, sans-serif'),
        }
    
    def get_heatmap_visual_map(self) -> Dict[str, Any]:
        """获取热力图视觉映射配置"""
        return {
            'min': 0,
            'max': 1,
            'calculable': True,
            'orient': 'horizontal',
            'left': 'center',
            'bottom': '5%',
            'inrange': {
                'color': ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', 
                         '#ffffcc', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
            },
            'text': ['高', '低'],
            'text_style': {
                'color': self.chart_theme.get('text_color', '#333333'),
                'font_size': 12,
            }
        }


# 默认深色科技主题配置
DEFAULT_THEME_CONFIG = {
    'color_palette': {
        'person': {
            'primary': '#1f77b4',
            'secondary': '#aec7e8'
        },
        'vehicle': {
            'primary': '#2ca02c',
            'secondary': '#98df8a'
        },
        'non_vehicle': {
            'primary': '#ff7f0e',
            'secondary': '#ffbb78'
        },
        'recall': '#00d4ff',
        'precision': '#00ff88',
        'f1': '#ff6b35',
        'gauge_good': '#00ff88',
        'gauge_medium': '#ffaa00',
        'gauge_poor': '#ff4444',
    },
    'chart_theme': {
        'background_color': '#1e1e1e',
        'secondary_bg': '#2d2d2d',
        'grid_color': '#404040',
        'text_color': '#ffffff',
        'secondary_text': '#cccccc',
        'accent_color': '#00d4ff',
        'font_family': 'Microsoft YaHei, Arial, sans-serif',
        'font_size': 12,
    }
}


def create_theme(config: Dict[str, Any] = None) -> ChartTheme:
    """
    创建图表主题
    
    Args:
        config: 配置字典，如果为None则使用默认配置
        
    Returns:
        ChartTheme实例
    """
    if config is None:
        config = DEFAULT_THEME_CONFIG
    return ChartTheme(config)
