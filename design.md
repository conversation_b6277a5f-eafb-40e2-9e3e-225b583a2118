# 系统设计文档（Design Specification）✅

## 1. 总体架构 ✅
```
数据 Excel  →  数据加载 Loader  →  指标计算 Metrics  →  图表生成 ECharts (原生JS)  →  HTML模板  →  静态 HTML
```
- ✅ 松耦合、分层设计；每层仅依赖下一层的清晰接口。
- ✅ 所有中间结果使用 pandas.DataFrame，方便调试与单元测试。
- ✅ 已验证大规模数据处理能力：50个场景，21,120行数据。

## 2. 模块划分 ✅
| 模块 | 主要职责 | 关键输入 | 关键输出 | 状态 |
|------|----------|----------|----------|------|
| **excel_loader.py** | 读取多场景 Excel，字段映射、距离分桶、类别映射 | Excel 文件列表 | 原始 DataFrame (raw_df) | ✅ |
| **calculator.py** | 计算 recall、precision、f1，生成聚合表 | raw_df | metrics_df (宽表) | ✅ |
| **charts_single.py** | 单场景图表函数集合 | metrics_df (过滤后) | ECharts HTML | ✅ |
| **charts_master.py** | 全场景图表函数集合 | metrics_df | ECharts HTML | ✅ |
| **theme.py** | 定义柔和扁平化配色、字体、背景 | 无 | theme 配置 | ✅ |
| **generate_report.py** | CLI 入口，调用以上模块并生成HTML | 命令行参数 | 52个HTML文件 | ✅ |

## 3. 数据结构 ✅
```python
# 原始行级记录
cols = [
    'scene_id', 'timestamp', 'distance_bin', 'target_cls',
    'tp', 'fp', 'fn', 'scene_name', 'target_cls_name'
]

# 计算后新增列
df['recall'] = df.tp / (df.tp + df.fn)
df['precision'] = df.tp / (df.tp + df.fp)
df['f1'] = 2 * df.recall * df.precision / (df.recall + df.precision)
```

### 聚合示例 ✅
```python
# 6种维度聚合已实现
aggregations = {
    'scene_distance_category': ['scene_id', 'distance_bin', 'target_cls_name'],
    'scene': ['scene_id'],
    'scene_distance': ['scene_id', 'distance_bin'],
    'scene_category': ['scene_id', 'target_cls_name'],
    'distance_category': ['distance_bin', 'target_cls_name'],
    'overall': []
}

# 时间序列数据（用于时间趋势图）
time_series_data = df.groupby(['timestamp', 'distance_bin', 'target_cls_name']).agg({
    'recall': 'mean',
    'precision': 'mean'
}).reset_index()
```

## 4. 图表设计

### 4.1 单场景分析页面
| 图表 | ECharts 类型 | 说明 |
|------|-------------|------|
| 指针式表盘 | Gauge | 3个表盘显示总体检出率、准确率、F1分数，0-100%刻度，颜色阈值 |
| 分组柱状图 | Bar | 4个距离段组，每组6根柱子(3类别×2指标)，Y轴0-100% |
| 雷达图+切换 | Radar | 单个雷达图6个维度，下方切换按钮选择距离段 |
| 时间趋势图+切换 | Line | X轴帧号，Y轴0-100%，距离段切换，每段6条线 |

### 4.2 全场景分析页面
| 图表 | 类型 | 说明 |
|------|------|------|
| 总体概览卡片 | HTML卡片 | 4张卡片：平均总体准确率、检出率、F1、场景数 |
| 分类别概览卡片 | HTML卡片 | 6张卡片：人/车/非的平均准确率和检出率 |
| 场景趋势图+切换 | Line | X轴场景名，Y轴0-100%，距离段切换，每段6条线 |

### 4.3 视觉风格设计
**商务科技风格**：
- 深色背景主题 (#1e1e1e, #2d2d2d)
- 浅灰色网格线 (#404040)
- 白色/浅灰色文字
- 科技蓝强调色 (#00d4ff)

**颜色映射**：
- 人：蓝色系 (#1f77b4, #aec7e8)
- 车：绿色系 (#2ca02c, #98df8a)
- 非机动车：橙色系 (#ff7f0e, #ffbb78)
- 准确率：实线，检出率：虚线

## 5. Page 拼版策略
1. **首页 (index.html)**：
   - 「全场景总览」图表集合。
   - 目录导航（锚点）链接至各场景页。
2. **场景子页 (scene_{id}.html)**：
   - 单场景图表集合。
3. **多页输出**：使用 pyecharts `Page(layout="simple")` 渲染，每页一个 Page → `render(path)`。
4. HTML 内引用 CDN 版本 ECharts；若离线需求，可下载资源至 `static/` 并修改模板。

## 6. 配置文件
```yaml
# config.yaml
scene_mapping: { "deqing-001": "德清场景001", "deqing-002": "德清场景002" }
distance_bins:
  - { name: "总体", min: 0, max: 150 }
  - { name: "0-50米", min: 0, max: 50 }
  - { name: "50-100米", min: 50, max: 100 }
  - { name: "100-150米", min: 100, max: 150 }
category_mapping: { "车": "机动车", "非机动车": "非机动车", "行人": "行人" }
color_palette:
  person: ["#1f77b4", "#aec7e8"]    # 蓝色系
  vehicle: ["#2ca02c", "#98df8a"]   # 绿色系
  non_vehicle: ["#ff7f0e", "#ffbb78"] # 橙色系
theme:
  background: "#1e1e1e"
  grid: "#404040"
  text: "#ffffff"
  accent: "#00d4ff"
```

## 7. 关键流程时序图
```mermaid
graph TD
A[启动 generate_report.py] --> B[加载配置]
B --> C[扫描 data/ 目录]
C --> D[loader 读取 Excel -> raw_df]
D --> E[metrics 计算指标 -> metrics_df]
E --> F1[charts_master 生成全场景图]
E --> F2[循环各 scene_id 调 charts_single]
F1 & F2 --> G[Page 渲染 HTML]
G --> H[输出到 report/]
```

## 8. 依赖与版本
- Python ≥ 3.8
- pyecharts ≥ 2.0
- pandas ≥ 1.3
- openpyxl ≥ 3.1

## 9. 性能与优化
- 数据量大时减少前端点数：后端采样或 ECharts `large`。
- Page 使用 `simple` 布局避免 DOM 过多。
- 同场景图表复用配色常量，减少内存占用。

## 10. 安全与隐私
- 报告为本地静态文件，不上传数据。
- 输出目录默认 `.gitignore`，防止机密数据误传。

---
