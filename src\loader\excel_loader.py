"""
Excel数据加载模块
负责读取多场景Excel文件，进行字段映射、数据清洗和距离分桶处理
"""

import pandas as pd
import numpy as np
import os
import glob
from typing import List, Dict, Any, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ExcelDataLoader:
    """Excel数据加载器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化数据加载器
        
        Args:
            config: 配置字典，包含字段映射、距离分桶等配置
        """
        self.config = config
        self.scene_mapping = config.get('scene_mapping', {})
        self.distance_bins = config.get('distance_bins', [])
        self.category_mapping = config.get('category_mapping', {})
        self.field_mapping = config.get('field_mapping', {})
        
    def load_excel_files(self, data_dir: str) -> pd.DataFrame:
        """
        批量加载Excel文件
        
        Args:
            data_dir: 数据目录路径
            
        Returns:
            合并后的DataFrame
        """
        excel_files = glob.glob(os.path.join(data_dir, "*.xlsx"))
        if not excel_files:
            raise ValueError(f"在目录 {data_dir} 中未找到Excel文件")
            
        all_data = []
        
        for file_path in excel_files:
            logger.info(f"正在加载文件: {file_path}")
            try:
                file_data = self._load_single_excel(file_path)
                all_data.append(file_data)
            except Exception as e:
                logger.warning(f"加载文件 {file_path} 失败: {e}")
                continue
                
        if not all_data:
            raise ValueError("没有成功加载任何Excel文件")
            
        # 合并所有数据
        combined_data = pd.concat(all_data, ignore_index=True)
        logger.info(f"成功加载 {len(excel_files)} 个文件，共 {len(combined_data)} 行数据")
        
        return combined_data
    
    def _load_single_excel(self, file_path: str) -> pd.DataFrame:
        """
        加载单个Excel文件
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            处理后的DataFrame
        """
        # 从文件名提取场景ID
        scene_id = os.path.splitext(os.path.basename(file_path))[0]
        
        # 读取Excel文件的所有sheet
        xl = pd.ExcelFile(file_path)
        sheet_data = []
        
        for sheet_name in xl.sheet_names:
            if sheet_name == '总结':  # 跳过总结sheet
                continue
                
            logger.info(f"  处理sheet: {sheet_name}")
            try:
                df = self._process_sheet(file_path, sheet_name, scene_id)
                if df is not None and not df.empty:
                    sheet_data.append(df)
            except Exception as e:
                logger.warning(f"  处理sheet {sheet_name} 失败: {e}")
                continue
        
        if not sheet_data:
            raise ValueError(f"文件 {file_path} 中没有有效的数据sheet")
            
        # 合并同一文件的所有sheet数据
        file_data = pd.concat(sheet_data, ignore_index=True)
        return file_data
    
    def _process_sheet(self, file_path: str, sheet_name: str, scene_id: str) -> Optional[pd.DataFrame]:
        """
        处理单个sheet的数据

        Args:
            file_path: Excel文件路径
            sheet_name: sheet名称
            scene_id: 场景ID

        Returns:
            处理后的DataFrame
        """
        try:
            # 读取sheet数据
            df = pd.read_excel(file_path, sheet_name=sheet_name, header=0)

            if df.empty:
                logger.warning(f"Sheet {sheet_name} 为空")
                return None

            # 解析数据结构
            # 第一行包含列标题信息
            if len(df) < 2:
                logger.warning(f"Sheet {sheet_name} 数据行数不足")
                return None

            # 提取列标题
            header_row = df.iloc[0]
            data_rows = df.iloc[1:].copy()

            # 尝试智能识别列结构
            column_mapping = self._detect_column_structure(df, header_row)
            if not column_mapping:
                logger.warning(f"无法识别Sheet {sheet_name} 的列结构，使用默认映射")
                column_mapping = self._get_default_column_mapping()

            # 重构数据为长格式
            processed_data = []
        
            for category, col_indices in column_mapping.items():
                if not col_indices or len(df.columns) <= max(col_indices):
                    logger.warning(f"类别 {category} 的列索引超出范围，跳过")
                    continue

                for idx, row in data_rows.iterrows():
                    if pd.isna(row.iloc[0]) or str(row.iloc[0]).strip() in ['序号', '', 'NaN']:  # 跳过无效行
                        continue

                    try:
                        # 提取数据
                        timestamp = self._safe_convert_to_int(row.iloc[0], idx)
                        true_count = self._safe_convert_to_float(row.iloc[col_indices[0]])
                        detected_count = self._safe_convert_to_float(row.iloc[col_indices[1]])
                        false_count = self._safe_convert_to_float(row.iloc[col_indices[2]])

                        # 计算TP, FP, FN
                        tp = detected_count  # 检出数即为TP
                        fp = false_count     # 误检数即为FP
                        fn = max(0, true_count - detected_count)  # FN = 真实数 - 检出数

                        processed_data.append({
                            'scene_id': scene_id,
                            'timestamp': timestamp,
                            'distance_bin': sheet_name,
                            'target_cls': category,
                            'tp': tp,
                            'fp': fp,
                            'fn': fn,
                            'true_count': true_count,
                            'detected_count': detected_count,
                            'false_count': false_count
                        })

                    except (ValueError, IndexError) as e:
                        logger.warning(f"处理行数据失败 (行{idx}): {e}")
                        continue

            if not processed_data:
                return None

            result_df = pd.DataFrame(processed_data)
            return result_df

        except Exception as e:
            logger.error(f"处理Sheet {sheet_name} 时发生错误: {e}")
            return None
    
    def clean_and_validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        数据清洗和验证
        
        Args:
            df: 原始DataFrame
            
        Returns:
            清洗后的DataFrame
        """
        logger.info("开始数据清洗和验证...")
        
        # 删除空值行
        initial_count = len(df)
        df = df.dropna(subset=['scene_id', 'target_cls', 'distance_bin'])
        logger.info(f"删除空值行: {initial_count - len(df)} 行")
        
        # 确保数值列为数值类型
        numeric_columns = ['tp', 'fp', 'fn', 'true_count', 'detected_count', 'false_count']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
        
        # 应用场景映射
        if self.scene_mapping:
            df['scene_name'] = df['scene_id'].map(self.scene_mapping).fillna(df['scene_id'])
        else:
            df['scene_name'] = df['scene_id']
            
        # 应用类别映射
        if self.category_mapping:
            df['target_cls_name'] = df['target_cls'].map(self.category_mapping).fillna(df['target_cls'])
        else:
            df['target_cls_name'] = df['target_cls']
        
        # 验证数据合理性
        df = self._validate_data(df)
        
        logger.info(f"数据清洗完成，最终数据量: {len(df)} 行")
        return df
    
    def _validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        验证数据合理性
        
        Args:
            df: DataFrame
            
        Returns:
            验证后的DataFrame
        """
        # 确保TP, FP, FN非负
        for col in ['tp', 'fp', 'fn']:
            if col in df.columns:
                df[col] = df[col].clip(lower=0)
        
        # 检查异常值
        invalid_rows = df[(df['tp'] + df['fn']) == 0]
        if len(invalid_rows) > 0:
            logger.warning(f"发现 {len(invalid_rows)} 行数据TP+FN=0，将被过滤")
            df = df[(df['tp'] + df['fn']) > 0]
        
        return df

    def _detect_column_structure(self, df: pd.DataFrame, header_row: pd.Series) -> Optional[Dict[str, List[int]]]:
        """
        智能检测Excel列结构

        Args:
            df: DataFrame
            header_row: 表头行

        Returns:
            列映射字典，格式为 {category: [true_count_col, detected_count_col, false_count_col]}
        """
        try:
            # 尝试通过表头文字识别列
            column_mapping = {}

            # 定义关键词映射
            keywords = {
                '车': ['车', '机动车', 'vehicle'],
                '非机动车': ['非机动车', 'bicycle', 'bike'],
                '行人': ['行人', 'person', 'pedestrian']
            }

            for category, category_keywords in keywords.items():
                cols = []
                for i, header_text in enumerate(header_row):
                    if pd.isna(header_text):
                        continue
                    header_str = str(header_text).lower()

                    # 检查是否包含类别关键词
                    if any(keyword in header_str for keyword in category_keywords):
                        # 找到类别相关列，尝试识别数据类型
                        if '真实' in header_str or 'true' in header_str:
                            if len(cols) == 0:
                                cols.append(i)
                        elif '检出' in header_str or 'detect' in header_str:
                            if len(cols) == 1:
                                cols.append(i)
                        elif '误检' in header_str or 'false' in header_str:
                            if len(cols) == 2:
                                cols.append(i)

                if len(cols) >= 3:
                    column_mapping[category] = cols[:3]

            return column_mapping if column_mapping else None

        except Exception as e:
            logger.warning(f"列结构检测失败: {e}")
            return None

    def _get_default_column_mapping(self) -> Dict[str, List[int]]:
        """
        获取默认列映射（向后兼容）

        Returns:
            默认列映射字典
        """
        return {
            '车': [1, 2, 3],  # 真实数, 检出数, 误检数
            '非机动车': [6, 7, 8],
            '行人': [11, 12, 13]
        }

    def _safe_convert_to_int(self, value, default=0) -> int:
        """
        安全转换为整数

        Args:
            value: 待转换值
            default: 默认值

        Returns:
            转换后的整数
        """
        try:
            if pd.isna(value):
                return default
            return int(float(value))
        except (ValueError, TypeError):
            return default

    def _safe_convert_to_float(self, value, default=0.0) -> float:
        """
        安全转换为浮点数

        Args:
            value: 待转换值
            default: 默认值

        Returns:
            转换后的浮点数
        """
        try:
            if pd.isna(value):
                return default
            return float(value)
        except (ValueError, TypeError):
            return default


def load_config(config_path: str) -> Dict[str, Any]:
    """
    加载配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        配置字典
    """
    import yaml
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        return {}


if __name__ == "__main__":
    # 测试代码
    config_path = "../../config/config.yaml"
    data_dir = "../../data"
    
    config = load_config(config_path)
    loader = ExcelDataLoader(config)
    
    try:
        df = loader.load_excel_files(data_dir)
        df = loader.clean_and_validate_data(df)
        
        print("数据加载成功!")
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        print("\n前5行数据:")
        print(df.head())
        
        print("\n数据统计:")
        print(f"场景数: {df['scene_id'].nunique()}")
        print(f"距离段数: {df['distance_bin'].nunique()}")
        print(f"目标类别数: {df['target_cls'].nunique()}")
        
    except Exception as e:
        print(f"数据加载失败: {e}")
