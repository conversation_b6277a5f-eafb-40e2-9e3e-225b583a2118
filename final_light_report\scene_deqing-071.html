
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>deqing-071 - 性能分析报告</title>
                <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
                <style>
                    body {
                        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                        color: #1e293b;
                        font-family: Microsoft YaHei, 'Segoe UI', Arial, sans-serif;
                        margin: 0;
                        padding: 20px;
                        min-height: 100vh;
                    }
                    .section {
                        margin: 30px 0;
                        padding: 25px;
                        background: #ffffff;
                        border-radius: 16px;
                        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.1);
                        border: 1px solid #e2e8f0;
                        backdrop-filter: blur(10px);
                        transition: transform 0.2s ease, box-shadow 0.2s ease;
                    }
                    .section:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08);
                    }
                    .gauge-container {
                        display: flex;
                        justify-content: space-around;
                        align-items: center;
                        flex-wrap: wrap;
                    }
                    h1 {
                        text-align: center;
                        color: #1e293b;
                        font-weight: 600;
                        font-size: 2.5rem;
                        margin-bottom: 10px;
                        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        background-clip: text;
                    }
                    h2 {
                        text-align: center;
                        color: #64748b;
                        font-weight: 500;
                        font-size: 1.8rem;
                        margin-bottom: 20px;
                    }
                </style>
            </head>
            <body>
                <h1>deqing-071 - 性能分析报告</h1>

                <div class="section">
                    <h2>总体性能指标</h2>
                    
        <div id="gauge-container-deqing-071" style="width: 100%; height: 400px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: #2c3e50; margin-bottom: 15px;">
                    deqing-071 - 总体性能仪表盘
                </h3>
            </div>
            <div id="gauge-chart-deqing-071" style="width: 100%; height: 350px;"></div>
        </div>

        <script>
            // 初始化仪表盘
            var gaugeChart_deqing_071 = echarts.init(document.getElementById('gauge-chart-deqing-071'));

            var gaugeOption_deqing_071 = {
                backgroundColor: 'rgba(255, 255, 255, 0.5)',
                series: [
                    
            {
                name: '检出率',
                type: 'gauge',
                center: ['25%', '50%'],
                radius: '70%',
                min: 0,
                max: 100,
                splitNumber: 10,
                axisLine: {
                    lineStyle: {
                        color: [[0.3, '#e67e22'], [0.7, '#3498db'], [1, '#3498db']],
                        width: 8
                    }
                },
                pointer: {
                    itemStyle: { color: 'auto' }
                },
                axisTick: {
                    distance: -30,
                    length: 8,
                    lineStyle: { color: '#fff', width: 2 }
                },
                splitLine: {
                    distance: -30,
                    length: 30,
                    lineStyle: { color: '#fff', width: 4 }
                },
                axisLabel: {
                    color: 'auto',
                    distance: 40,
                    fontSize: 12
                },
                detail: {
                    valueAnimation: true,
                    formatter: '{value}%',
                    color: '#3498db',
                    fontSize: 16,
                    offsetCenter: [0, '70%']
                },
                title: {
                    offsetCenter: [0, '90%'],
                    fontSize: 14,
                    color: '#5a6c7d'
                },
                data: [{ value: 64.3, name: '检出率' }]
            }
            ,
                    
            {
                name: '准确率',
                type: 'gauge',
                center: ['75%', '50%'],
                radius: '70%',
                min: 0,
                max: 100,
                splitNumber: 10,
                axisLine: {
                    lineStyle: {
                        color: [[0.3, '#e67e22'], [0.7, '#f39c12'], [1, '#2ecc71']],
                        width: 8
                    }
                },
                pointer: {
                    itemStyle: { color: 'auto' }
                },
                axisTick: {
                    distance: -30,
                    length: 8,
                    lineStyle: { color: '#fff', width: 2 }
                },
                splitLine: {
                    distance: -30,
                    length: 30,
                    lineStyle: { color: '#fff', width: 4 }
                },
                axisLabel: {
                    color: 'auto',
                    distance: 40,
                    fontSize: 12
                },
                detail: {
                    valueAnimation: true,
                    formatter: '{value}%',
                    color: '#2ecc71',
                    fontSize: 16,
                    offsetCenter: [0, '70%']
                },
                title: {
                    offsetCenter: [0, '90%'],
                    fontSize: 14,
                    color: '#5a6c7d'
                },
                data: [{ value: 82.6, name: '准确率' }]
            }
            
                ]
            };

            gaugeChart_deqing_071.setOption(gaugeOption_deqing_071);
            window.addEventListener('resize', () => gaugeChart_deqing_071.resize());
        </script>
        
                </div>

                <div class="section">
                    <h2>距离段性能对比</h2>
                    
        <div id="bar-chart-deqing_071" style="width: 1000px; height: 500px; margin: 20px auto;"></div>

        <script>
            // 初始化柱状图
            var barChart_deqing_071 = echarts.init(document.getElementById('bar-chart-deqing_071'));

            var barOption_deqing_071 = {
  "backgroundColor": "#f8fafc",
  "title": {
    "text": "deqing-071 - 距离段性能对比",
    "subtext": "不同目标类别在各距离段的准确率和检出率对比",
    "textStyle": {
      "color": "#1e293b",
      "fontSize": 16
    },
    "subtextStyle": {
      "color": "#64748b",
      "fontSize": 12
    }
  },
  "tooltip": {
    "trigger": "axis",
    "backgroundColor": "rgba(45, 45, 45, 0.95)",
    "borderColor": "#e2e8f0",
    "textStyle": {
      "color": "#ffffff"
    }
  },
  "legend": {
    "top": "8%",
    "textStyle": {
      "color": "#1e293b"
    }
  },
  "xAxis": {
    "type": "category",
    "data": [
      "0-50米",
      "50-100米",
      "总体"
    ],
    "name": "距离段",
    "axisLabel": {
      "color": "#1e293b"
    },
    "axisLine": {
      "lineStyle": {
        "color": "#e2e8f0"
      }
    }
  },
  "yAxis": {
    "type": "value",
    "name": "性能指标 (%)",
    "min": 0,
    "max": 100,
    "axisLabel": {
      "color": "#1e293b"
    },
    "axisLine": {
      "lineStyle": {
        "color": "#e2e8f0"
      }
    },
    "splitLine": {
      "show": true,
      "lineStyle": {
        "color": "#e2e8f0",
        "type": "dashed"
      }
    }
  },
  "series": [
    {
      "name": "机动车-准确率",
      "type": "bar",
      "data": [
        83.5,
        75.3,
        79.6
      ],
      "itemStyle": {
        "color": "#10b981"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "机动车-检出率",
      "type": "bar",
      "data": [
        67.2,
        57.2,
        62.4
      ],
      "itemStyle": {
        "color": "#6ee7b7"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "行人-准确率",
      "type": "bar",
      "data": [
        95.2,
        88.1,
        91.7
      ],
      "itemStyle": {
        "color": "#3b82f6"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "行人-检出率",
      "type": "bar",
      "data": [
        77.3,
        70.0,
        73.7
      ],
      "itemStyle": {
        "color": "#93c5fd"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "非机动车-准确率",
      "type": "bar",
      "data": [
        79.4,
        71.6,
        75.6
      ],
      "itemStyle": {
        "color": "#f59e0b"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    },
    {
      "name": "非机动车-检出率",
      "type": "bar",
      "data": [
        61.6,
        51.6,
        56.5
      ],
      "itemStyle": {
        "color": "#fbbf24"
      },
      "label": {
        "show": true,
        "position": "top",
        "color": "#ffffff"
      }
    }
  ]
};

            barChart_deqing_071.setOption(barOption_deqing_071);
            window.addEventListener('resize', () => barChart_deqing_071.resize());
        </script>
        
                </div>

                <div class="section">
                    <h2>雷达图分析</h2>
                    
        <style>
            .radar-btn:hover {
                background: #3b82f6 !important;
                color: white !important;
                border-color: #3b82f6 !important;
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(59,130,246,0.3) !important;
            }
            .radar-active {
                background: #3b82f6 !important;
                color: white !important;
                border-color: #3b82f6 !important;
                box-shadow: 0 4px 12px rgba(59,130,246,0.3) !important;
            }
        </style>
        <div id="radar-container-deqing-071" style="width: 100%; height: 600px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: #1e293b; margin-bottom: 15px;">
                    deqing-071 - 雷达图分析
                </h3>
                <div id="radar-buttons-deqing-071" style="margin-bottom: 20px;">
        
                    <button onclick="switchRadar_deqing_071('0-50米', this)"
                            class="radar-btn radar-active"
                            style="margin: 0 10px; padding: 10px 20px;
                                   background: #ffffff;
                                   color: #1e293b;
                                   border: 2px solid #e2e8f0;
                                   border-radius: 8px; cursor: pointer;
                                   font-weight: 500; transition: all 0.2s ease;
                                   box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        0-50米
                    </button>
            
                    <button onclick="switchRadar_deqing_071('50-100米', this)"
                            class="radar-btn "
                            style="margin: 0 10px; padding: 10px 20px;
                                   background: #ffffff;
                                   color: #1e293b;
                                   border: 2px solid #e2e8f0;
                                   border-radius: 8px; cursor: pointer;
                                   font-weight: 500; transition: all 0.2s ease;
                                   box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        50-100米
                    </button>
            
                    <button onclick="switchRadar_deqing_071('总体', this)"
                            class="radar-btn "
                            style="margin: 0 10px; padding: 10px 20px;
                                   background: #ffffff;
                                   color: #1e293b;
                                   border: 2px solid #e2e8f0;
                                   border-radius: 8px; cursor: pointer;
                                   font-weight: 500; transition: all 0.2s ease;
                                   box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        总体
                    </button>
            
                </div>
            </div>
            <div id="radar-chart-deqing-071" style="width: 100%; height: 500px;"></div>
        </div>

        <script>
            // 雷达图数据
            var radarData_deqing_071 = {"0-50米": [83.46055979643766, 67.21311475409836, 95.21410579345087, 77.30061349693251, 79.40379403794037, 61.5546218487395], "50-100米": [75.2840909090909, 57.2354211663067, 88.05194805194805, 70.0413223140496, 71.59420289855073, 51.56576200417536], "总体": [79.59731543624162, 62.35541535226078, 91.68797953964194, 73.6896197327852, 75.63025210084034, 56.54450261780105]};

            // 雷达图配置
            var radarOption_deqing_071 = {
                backgroundColor: '#f8fafc',
                radar: {
                    indicator: [
                        {name: '人-准确率', max: 100},
                        {name: '人-检出率', max: 100},
                        {name: '车-准确率', max: 100},
                        {name: '车-检出率', max: 100},
                        {name: '非-准确率', max: 100},
                        {name: '非-检出率', max: 100}
                    ],
                    center: ['50%', '50%'],
                    radius: '70%',
                    axisName: {
                        color: '#1e293b',
                        fontSize: 12
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#e2e8f0'
                        }
                    },
                    splitArea: {
                        show: false
                    }
                },
                series: [{
                    type: 'radar',
                    data: [{
                        value: radarData_deqing_071['0-50米'],
                        name: '0-50米',
                        areaStyle: {
                            color: 'rgba(0, 212, 255, 0.3)'
                        },
                        lineStyle: {
                            color: '#3b82f6'
                        }
                    }]
                }]
            };

            // 初始化雷达图
            var radarChart_deqing_071 = echarts.init(document.getElementById('radar-chart-deqing-071'));
            radarChart_deqing_071.setOption(radarOption_deqing_071);

            // 切换函数
            function switchRadar_deqing_071(distBin, clickedButton) {
                // 更新按钮状态
                var buttons = document.querySelectorAll('#radar-buttons-deqing-071 .radar-btn');
                buttons.forEach(btn => btn.classList.remove('radar-active'));
                clickedButton.classList.add('radar-active');

                // 更新雷达图数据
                radarOption_deqing_071.series[0].data[0].value = radarData_deqing_071[distBin];
                radarOption_deqing_071.series[0].data[0].name = distBin;
                radarChart_deqing_071.setOption(radarOption_deqing_071);
            }
        </script>

        <style>
            .radar-btn.radar-active {
                background: #3b82f6 !important;
                color: #000000 !important;
            }
            .radar-btn:hover {
                background: #3b82f6 !important;
                color: #000000 !important;
            }
        </style>
        
                </div>

                <div class="section">
                    <h2>时间趋势分析</h2>
                    
        <style>
            .trend-btn:hover {
                background: #3b82f6 !important;
                color: white !important;
                border-color: #3b82f6 !important;
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(59,130,246,0.3) !important;
            }
            .trend-active {
                background: #3b82f6 !important;
                color: white !important;
                border-color: #3b82f6 !important;
                box-shadow: 0 4px 12px rgba(59,130,246,0.3) !important;
            }
        </style>
        <div id="trend-container-deqing-071" style="width: 100%; height: 700px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: #1e293b; margin-bottom: 15px;">
                    deqing-071 - 时间趋势分析
                </h3>
                <div id="trend-buttons-deqing-071" style="margin-bottom: 20px;">
        
                    <button onclick="switchTrend_deqing_071('0-50米', this)"
                            class="trend-btn trend-active"
                            style="margin: 0 10px; padding: 10px 20px;
                                   background: #ffffff;
                                   color: #1e293b;
                                   border: 2px solid #e2e8f0;
                                   border-radius: 8px; cursor: pointer;
                                   font-weight: 500; transition: all 0.2s ease;
                                   box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        0-50米
                    </button>
            
                    <button onclick="switchTrend_deqing_071('50-100米', this)"
                            class="trend-btn "
                            style="margin: 0 10px; padding: 10px 20px;
                                   background: #ffffff;
                                   color: #1e293b;
                                   border: 2px solid #e2e8f0;
                                   border-radius: 8px; cursor: pointer;
                                   font-weight: 500; transition: all 0.2s ease;
                                   box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        50-100米
                    </button>
            
                    <button onclick="switchTrend_deqing_071('总体', this)"
                            class="trend-btn "
                            style="margin: 0 10px; padding: 10px 20px;
                                   background: #ffffff;
                                   color: #1e293b;
                                   border: 2px solid #e2e8f0;
                                   border-radius: 8px; cursor: pointer;
                                   font-weight: 500; transition: all 0.2s ease;
                                   box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        总体
                    </button>
            
                </div>
            </div>
            <div id="trend-chart-deqing-071" style="width: 100%; height: 600px;"></div>
        </div>

        <script>
            // 时间戳数据
            var timestamps_deqing_071 = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75];

            // 趋势图数据
            var trendData_deqing_071 = {"0-50米": {"机动车-准确率": [66.7, 100.0, 100.0, 100.0, 75.0, 80.0, 100.0, 100.0, 80.0, 87.5, 75.0, 77.8, 80.0, 71.4, 75.0, 80.0, 100.0, 66.7, 80.0, 83.3, 100.0, 100.0, 75.0, 71.4, 100.0, 100.0, 77.8, 83.3, 100.0, 100.0, 85.7, 100.0, 77.8, 71.4, 88.9, 66.7, 83.3, 100.0, 80.0, 80.0, 83.3, 100.0, 100.0, 75.0, 70.0, 83.3, 75.0, 70.0, 100.0, 80.0, 100.0, 100.0, 100.0, 85.7, 77.8, 100.0, 66.7, 100.0, 75.0, 83.3, 80.0, 75.0, 100.0, 83.3, 100.0, 80.0, 100.0, 100.0, 100.0, 87.5, 100.0, 100.0, 75.0, 100.0], "机动车-检出率": [66.7, 50.0, 57.1, 50.0, 60.0, 57.1, 66.7, 75.0, 66.7, 77.8, 75.0, 70.0, 57.1, 71.4, 60.0, 57.1, 71.4, 88.9, 66.7, 62.5, 66.7, 60.0, 75.0, 62.5, 60.0, 75.0, 77.8, 55.6, 71.4, 33.3, 66.7, 66.7, 70.0, 62.5, 80.0, 85.7, 55.6, 66.7, 66.7, 57.1, 55.6, 50.0, 60.0, 75.0, 70.0, 83.3, 85.7, 87.5, 50.0, 57.1, 70.0, 57.1, 50.0, 85.7, 70.0, 75.0, 66.7, 60.0, 60.0, 71.4, 80.0, 60.0, 66.7, 55.6, 57.1, 66.7, 75.0, 60.0, 66.7, 70.0, 77.8, 71.4, 75.0, 85.7], "行人-准确率": [100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 83.3, 88.9, 83.3, 88.9, 81.8, 88.9, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 87.5, 100.0, 85.7, 100.0, 100.0, 85.7, 88.9, 85.7, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 87.5, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 88.9, 87.5, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 88.9, 100.0, 87.5, 87.5, 100.0, 87.5], "行人-检出率": [80.0, 71.4, 60.0, 87.5, 75.0, 83.3, 60.0, 71.4, 75.0, 66.7, 85.7, 83.3, 87.5, 71.4, 80.0, 83.3, 80.0, 90.0, 88.9, 85.7, 83.3, 66.7, 71.4, 80.0, 80.0, 60.0, 87.5, 75.0, 66.7, 83.3, 87.5, 87.5, 66.7, 75.0, 66.7, 66.7, 75.0, 80.0, 75.0, 77.8, 66.7, 66.7, 71.4, 66.7, 60.0, 66.7, 75.0, 83.3, 75.0, 83.3, 77.8, 83.3, 71.4, 80.0, 87.5, 75.0, 71.4, 80.0, 80.0, 88.9, 77.8, 71.4, 70.0, 66.7, 75.0, 66.7, 87.5, 66.7, 80.0, 75.0, 77.8, 70.0, 71.4, 87.5], "非机动车-准确率": [66.7, 75.0, 100.0, 100.0, 80.0, 66.7, 88.9, 83.3, 80.0, 100.0, 75.0, 71.4, 100.0, 83.3, 71.4, 71.4, 100.0, 85.7, 66.7, 66.7, 100.0, 75.0, 71.4, 80.0, 83.3, 100.0, 71.4, 100.0, 83.3, 66.7, 83.3, 100.0, 80.0, 75.0, 62.5, 87.5, 75.0, 77.8, 100.0, 100.0, 100.0, 71.4, 100.0, 66.7, 66.7, 100.0, 83.3, 66.7, 100.0, 100.0, 100.0, 100.0, 66.7, 100.0, 83.3, 87.5, 66.7, 71.4, 75.0, 66.7, 100.0, 100.0, 100.0, 100.0, 71.4, 72.7, 100.0, 85.7, 80.0, 71.4, 66.7, 100.0, 80.0, 100.0], "非机动车-检出率": [57.1, 66.7, 80.0, 66.7, 66.7, 60.0, 80.0, 50.0, 80.0, 50.0, 66.7, 50.0, 40.0, 71.4, 71.4, 55.6, 33.3, 75.0, 50.0, 66.7, 50.0, 42.9, 71.4, 66.7, 62.5, 33.3, 55.6, 50.0, 55.6, 66.7, 55.6, 50.0, 66.7, 75.0, 71.4, 77.8, 50.0, 70.0, 66.7, 40.0, 33.3, 62.5, 33.3, 50.0, 66.7, 40.0, 71.4, 66.7, 50.0, 50.0, 50.0, 57.1, 66.7, 50.0, 62.5, 70.0, 80.0, 71.4, 75.0, 57.1, 60.0, 60.0, 60.0, 50.0, 71.4, 80.0, 50.0, 60.0, 44.4, 71.4, 66.7, 50.0, 66.7, 75.0]}, "50-100米": {"机动车-准确率": [100.0, 100.0, 100.0, 100.0, 66.7, 80.0, 85.7, 100.0, 75.0, 71.4, 83.3, 83.3, 100.0, 75.0, 66.7, 100.0, 55.6, 66.7, 55.6, 87.5, 85.7, 100.0, 66.7, 75.0, 100.0, 80.0, 75.0, 100.0, 71.4, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 66.7, 100.0, 75.0, 72.7, 85.7, 57.1, 75.0, 83.3, 100.0, 100.0, 66.7, 55.6, 66.7, 100.0, 60.0, 100.0, 66.7, 100.0, 100.0, 80.0, 100.0, 75.0, 100.0, 53.8, 83.3, 75.0, 66.7, 100.0, 61.5, 100.0, 60.0, 54.5, 100.0, 100.0, 66.7, 100.0, 66.7, 100.0], "机动车-检出率": [50.0, 50.0, 66.7, 60.0, 66.7, 66.7, 60.0, 25.0, 75.0, 50.0, 62.5, 55.6, 50.0, 37.5, 33.3, 33.3, 71.4, 57.1, 71.4, 70.0, 66.7, 66.7, 60.0, 50.0, 75.0, 44.4, 50.0, 33.3, 83.3, 40.0, 75.0, 66.7, 50.0, 33.3, 33.3, 33.3, 66.7, 50.0, 75.0, 80.0, 75.0, 44.4, 50.0, 62.5, 33.3, 66.7, 50.0, 71.4, 66.7, 50.0, 75.0, 25.0, 75.0, 60.0, 33.3, 50.0, 33.3, 60.0, 40.0, 70.0, 50.0, 60.0, 66.7, 50.0, 80.0, 42.9, 66.7, 66.7, 33.3, 57.1, 50.0, 40.0, 40.0, 50.0], "行人-准确率": [83.3, 88.9, 70.0, 75.0, 100.0, 100.0, 75.0, 80.0, 75.0, 100.0, 100.0, 83.3, 100.0, 100.0, 100.0, 100.0, 80.0, 100.0, 85.7, 100.0, 100.0, 100.0, 77.8, 100.0, 75.0, 83.3, 100.0, 100.0, 100.0, 75.0, 83.3, 80.0, 100.0, 100.0, 100.0, 85.7, 100.0, 75.0, 100.0, 70.0, 71.4, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 66.7, 100.0, 80.0, 100.0, 80.0, 100.0, 100.0, 75.0, 85.7, 75.0, 77.8, 100.0, 77.8, 100.0, 77.8, 100.0, 100.0, 100.0, 83.3, 100.0, 66.7, 100.0, 100.0, 100.0, 88.9, 100.0, 88.9], "行人-检出率": [62.5, 80.0, 77.8, 75.0, 71.4, 80.0, 50.0, 66.7, 60.0, 60.0, 85.7, 71.4, 66.7, 33.3, 57.1, 80.0, 80.0, 66.7, 85.7, 60.0, 80.0, 50.0, 70.0, 50.0, 50.0, 83.3, 60.0, 66.7, 60.0, 60.0, 71.4, 80.0, 66.7, 60.0, 66.7, 66.7, 71.4, 60.0, 66.7, 77.8, 71.4, 33.3, 57.1, 77.8, 80.0, 60.0, 70.0, 66.7, 77.8, 80.0, 80.0, 66.7, 50.0, 66.7, 75.0, 66.7, 66.7, 77.8, 60.0, 77.8, 62.5, 77.8, 66.7, 75.0, 85.7, 83.3, 66.7, 66.7, 60.0, 77.8, 60.0, 80.0, 85.7, 80.0], "非机动车-准确率": [100.0, 100.0, 66.7, 80.0, 75.0, 62.5, 62.5, 66.7, 75.0, 62.5, 100.0, 100.0, 75.0, 80.0, 75.0, 75.0, 100.0, 100.0, 83.3, 66.7, 100.0, 80.0, 66.7, 75.0, 75.0, 100.0, 80.0, 100.0, 100.0, 100.0, 57.1, 50.0, 66.7, 50.0, 71.4, 83.3, 71.4, 100.0, 100.0, 66.7, 60.0, 60.0, 100.0, 66.7, 66.7, 100.0, 60.0, 100.0, 80.0, 71.4, 50.0, 85.7, 100.0, 66.7, 83.3, 62.5, 66.7, 50.0, 75.0, 60.0, 100.0, 66.7, 85.7, 83.3, 100.0, 100.0, 100.0, 50.0, 75.0, 100.0, 66.7, 100.0, 50.0, 66.7], "非机动车-检出率": [60.0, 25.0, 66.7, 66.7, 42.9, 71.4, 55.6, 57.1, 50.0, 50.0, 42.9, 33.3, 66.7, 40.0, 66.7, 33.3, 50.0, 33.3, 50.0, 50.0, 25.0, 57.1, 40.0, 42.9, 60.0, 60.0, 57.1, 50.0, 40.0, 42.9, 50.0, 42.9, 50.0, 50.0, 55.6, 55.6, 55.6, 33.3, 33.3, 50.0, 66.7, 37.5, 40.0, 40.0, 50.0, 50.0, 75.0, 50.0, 66.7, 50.0, 60.0, 66.7, 33.3, 50.0, 62.5, 55.6, 33.3, 42.9, 75.0, 66.7, 25.0, 50.0, 60.0, 62.5, 33.3, 25.0, 33.3, 66.7, 60.0, 40.0, 60.0, 40.0, 75.0, 66.7]}, "总体": {"机动车-准确率": [83.3, 100.0, 100.0, 100.0, 70.8, 80.0, 92.9, 100.0, 77.5, 79.5, 79.2, 80.6, 90.0, 73.2, 70.8, 90.0, 77.8, 66.7, 67.8, 85.4, 92.9, 100.0, 70.8, 73.2, 100.0, 90.0, 76.4, 91.7, 85.7, 100.0, 92.9, 100.0, 88.9, 85.7, 94.4, 83.3, 75.0, 100.0, 77.5, 76.4, 84.5, 78.6, 87.5, 79.2, 85.0, 91.7, 70.8, 62.8, 83.3, 90.0, 80.0, 100.0, 83.3, 92.9, 88.9, 90.0, 83.3, 87.5, 87.5, 68.6, 81.7, 75.0, 83.3, 91.7, 80.8, 90.0, 80.0, 77.3, 100.0, 93.8, 83.3, 100.0, 70.8, 100.0], "机动车-检出率": [58.3, 50.0, 61.9, 55.0, 63.3, 61.9, 63.3, 50.0, 70.8, 63.9, 68.8, 62.8, 53.6, 54.5, 46.7, 45.2, 71.4, 73.0, 69.0, 66.2, 66.7, 63.3, 67.5, 56.2, 67.5, 59.7, 63.9, 44.4, 77.4, 36.7, 70.8, 66.7, 60.0, 47.9, 56.7, 59.5, 61.1, 58.3, 70.8, 68.6, 65.3, 47.2, 55.0, 68.8, 51.7, 75.0, 67.9, 79.5, 58.3, 53.6, 72.5, 41.1, 62.5, 72.9, 51.7, 62.5, 50.0, 60.0, 50.0, 70.7, 65.0, 60.0, 66.7, 52.8, 68.6, 54.8, 70.8, 63.3, 50.0, 63.6, 63.9, 55.7, 57.5, 67.9], "行人-准确率": [91.7, 94.4, 85.0, 87.5, 100.0, 100.0, 87.5, 90.0, 87.5, 100.0, 100.0, 91.7, 100.0, 91.7, 94.4, 91.7, 84.4, 90.9, 87.3, 100.0, 100.0, 100.0, 88.9, 100.0, 87.5, 91.7, 100.0, 100.0, 100.0, 87.5, 91.7, 83.8, 100.0, 92.9, 100.0, 92.9, 92.9, 81.9, 92.9, 85.0, 85.7, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 83.3, 100.0, 90.0, 93.8, 90.0, 100.0, 100.0, 87.5, 92.9, 87.5, 88.9, 100.0, 83.3, 93.8, 88.9, 100.0, 100.0, 100.0, 91.7, 100.0, 83.3, 94.4, 100.0, 93.8, 88.2, 100.0, 88.2], "行人-检出率": [71.2, 75.7, 68.9, 81.2, 73.2, 81.7, 55.0, 69.0, 67.5, 63.3, 85.7, 77.4, 77.1, 52.4, 68.6, 81.7, 80.0, 78.3, 87.3, 72.9, 81.7, 58.3, 70.7, 65.0, 65.0, 71.7, 73.8, 70.8, 63.3, 71.7, 79.5, 83.8, 66.7, 67.5, 66.7, 66.7, 73.2, 70.0, 70.8, 77.8, 69.0, 50.0, 64.3, 72.2, 70.0, 63.3, 72.5, 75.0, 76.4, 81.7, 78.9, 75.0, 60.7, 73.3, 81.2, 70.8, 69.0, 78.9, 70.0, 83.3, 70.1, 74.6, 68.3, 70.8, 80.4, 75.0, 77.1, 66.7, 70.0, 76.4, 68.9, 75.0, 78.6, 83.8], "非机动车-准确率": [83.3, 87.5, 83.3, 90.0, 77.5, 64.6, 75.7, 75.0, 77.5, 81.2, 87.5, 85.7, 87.5, 81.7, 73.2, 73.2, 100.0, 92.9, 75.0, 66.7, 100.0, 77.5, 69.0, 77.5, 79.2, 100.0, 75.7, 100.0, 91.7, 83.3, 70.2, 75.0, 73.3, 62.5, 67.0, 85.4, 73.2, 88.9, 100.0, 83.3, 80.0, 65.7, 100.0, 66.7, 66.7, 100.0, 71.7, 83.3, 90.0, 85.7, 75.0, 92.9, 83.3, 83.3, 83.3, 75.0, 66.7, 60.7, 75.0, 63.3, 100.0, 83.3, 92.9, 91.7, 85.7, 86.4, 100.0, 67.9, 77.5, 85.7, 66.7, 100.0, 65.0, 83.3], "非机动车-检出率": [58.6, 45.8, 73.3, 66.7, 54.8, 65.7, 67.8, 53.6, 65.0, 50.0, 54.8, 41.7, 53.3, 55.7, 69.0, 44.4, 41.7, 54.2, 50.0, 58.3, 37.5, 50.0, 55.7, 54.8, 61.3, 46.7, 56.3, 50.0, 47.8, 54.8, 52.8, 46.4, 58.3, 62.5, 63.5, 66.7, 52.8, 51.7, 50.0, 45.0, 50.0, 50.0, 36.7, 45.0, 58.3, 45.0, 73.2, 58.3, 58.3, 50.0, 55.0, 61.9, 50.0, 50.0, 62.5, 62.8, 56.7, 57.1, 75.0, 61.9, 42.5, 55.0, 60.0, 56.2, 52.4, 52.5, 41.7, 63.3, 52.2, 55.7, 63.3, 45.0, 70.8, 70.8]}};

            // 颜色配置
            var categoryColors_deqing_071 = {
                '行人': ['#3b82f6', '#93c5fd'],
                '机动车': ['#10b981', '#6ee7b7'],
                '非机动车': ['#f59e0b', '#fbbf24']
            };

            // 趋势图配置
            function getTrendOption_deqing_071(distBin) {
                var seriesData = trendData_deqing_071[distBin];
                var series = [];

                Object.keys(seriesData).forEach(function(seriesName) {
                    var category = seriesName.split('-')[0];
                    var metric = seriesName.split('-')[1];
                    var colorIndex = metric === '准确率' ? 0 : 1;
                    var lineType = metric === '准确率' ? 'solid' : 'dashed';

                    series.push({
                        name: seriesName,
                        type: 'line',
                        data: seriesData[seriesName],
                        smooth: false,
                        symbol: 'none',
                        lineStyle: {
                            color: categoryColors_deqing_071[category][colorIndex],
                            width: 3,
                            type: lineType
                        },
                        emphasis: {
                            focus: 'series',
                            lineStyle: {
                                width: 4
                            }
                        }
                    });
                });

                return {
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    title: {
                        text: distBin + ' - 性能趋势',
                        left: 'center',
                        textStyle: {
                            color: '#5a6c7d',
                            fontSize: 16
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        borderColor: '#bdc3c7',
                        textStyle: {
                            color: '#2c3e50'
                        }
                    },
                    legend: {
                        top: '8%',
                        textStyle: {
                            color: '#5a6c7d'
                        }
                    },
                    grid: {
                        left: '10%',
                        right: '10%',
                        top: '20%',
                        bottom: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: timestamps_deqing_071,
                        name: '帧号',
                        axisLabel: {
                            color: '#7f8c8d'
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#bdc3c7'
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '性能指标 (%)',
                        min: 0,
                        max: 100,
                        axisLabel: {
                            color: '#7f8c8d'
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#bdc3c7'
                            }
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#ecf0f1',
                                type: 'dashed'
                            }
                        }
                    },
                    series: series
                };
            }

            // 初始化趋势图
            var trendChart_deqing_071 = echarts.init(document.getElementById('trend-chart-deqing-071'));
            trendChart_deqing_071.setOption(getTrendOption_deqing_071('0-50米'));

            // 切换函数
            function switchTrend_deqing_071(distBin, clickedButton) {
                // 更新按钮状态
                var buttons = document.querySelectorAll('#trend-buttons-deqing-071 .trend-btn');
                buttons.forEach(btn => btn.classList.remove('trend-active'));
                clickedButton.classList.add('trend-active');

                // 更新趋势图
                trendChart_deqing_071.setOption(getTrendOption_deqing_071(distBin));
            }
        </script>

        <style>
            .trend-btn.trend-active {
                background: #3b82f6 !important;
                color: #000000 !important;
            }
            .trend-btn:hover {
                background: #3b82f6 !important;
                color: #000000 !important;
            }
        </style>
        
                </div>
            </body>
            </html>
            